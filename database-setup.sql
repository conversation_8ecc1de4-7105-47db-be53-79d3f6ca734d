-- Database setup script for FAPSI Hub
-- Run this in your Supabase SQL editor to create the required tables and policies

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  name TEXT,
  user_type TEXT CHECK (user_type IN ('student', 'master', 'admin')) DEFAULT 'student',
  status TEXT CHECK (status IN ('pending', 'approved', 'suspended')) DEFAULT 'pending',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  approved_by UUID REFERENCES auth.users(id),
  course TEXT,
  semester TEXT,
  phone TEXT,
  learning_goals TEXT,
  study_preferences TEXT,
  notifications_enabled BOOLEAN DEFAULT true
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Masters can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Masters can update student profiles" ON profiles;
DROP POLICY IF EXISTS "Allow profile creation" ON profiles;

-- Create RLS policies
CREATE POLICY "Users can view own profile" ON profiles 
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles 
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Masters can view all profiles" ON profiles 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND user_type IN ('master', 'admin')
    )
  );

CREATE POLICY "Masters can update student profiles" ON profiles 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND user_type IN ('master', 'admin')
    )
  );

CREATE POLICY "Allow profile creation" ON profiles 
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_user_type ON profiles(user_type);
CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);

-- Create a function to handle user profile creation on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, name, user_type, status)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', ''),
    COALESCE(NEW.raw_user_meta_data->>'user_type', 'student'),
    CASE 
      WHEN COALESCE(NEW.raw_user_meta_data->>'user_type', 'student') = 'master' THEN 'approved'
      ELSE 'pending'
    END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert test users if they don't exist
DO $$
BEGIN
  -- Check if master user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    -- Note: You'll need to create these users through the Supabase Auth interface
    -- or use the admin API, as we can't directly insert into auth.users from SQL
    RAISE NOTICE 'Please create test users through Supabase Auth:';
    RAISE NOTICE '1. <EMAIL> (password: master123)';
    RAISE NOTICE '2. <EMAIL> (password: aluno123)';
  END IF;
END $$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.profiles TO anon, authenticated;
