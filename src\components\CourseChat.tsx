import React, { useState, useEffect, useRef } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Send, MessageCircle, Users } from "lucide-react";
import { 
  getChatMessages, 
  sendChatMessage, 
  getCurrentUser, 
  getUserProfile 
} from "@/lib/supabase";

interface ChatMessage {
  id: string;
  message: string;
  created_at: string;
  sender: {
    name: string;
    user_type: string;
  };
  course_category?: string;
}

interface CourseChatProps {
  userCourse?: string;
}

export const CourseChat: React.FC<CourseChatProps> = ({ userCourse }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadMessages();
    loadCurrentUser();
  }, [userCourse]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadCurrentUser = async () => {
    try {
      const user = await getCurrentUser();
      setCurrentUser(user);
    } catch (error) {
      console.error("Erro ao carregar usuário:", error);
    }
  };

  const loadMessages = async () => {
    try {
      setLoading(true);
      const data = await getChatMessages(userCourse);
      setMessages(data);
    } catch (error) {
      console.error("Erro ao carregar mensagens:", error);
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim() || sending) return;

    try {
      setSending(true);
      await sendChatMessage({
        message: newMessage.trim(),
        course_category: userCourse
      });
      
      setNewMessage("");
      await loadMessages(); // Recarregar mensagens
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
    } finally {
      setSending(false);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getUserTypeColor = (userType: string) => {
    switch (userType) {
      case 'master':
        return 'text-purple-600 font-semibold';
      case 'admin':
        return 'text-red-600 font-semibold';
      default:
        return 'text-blue-600';
    }
  };

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'master':
        return 'Professor';
      case 'admin':
        return 'Admin';
      default:
        return 'Aluno';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <MessageCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <p>Carregando chat...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2">
          <MessageCircle className="h-5 w-5" />
          <span>Chat do Curso</span>
          {userCourse && (
            <span className="text-sm font-normal text-muted-foreground">
              • {userCourse}
            </span>
          )}
        </CardTitle>
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Users className="h-4 w-4" />
          <span>Apenas alunos do seu curso podem ver estas mensagens</span>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-4 space-y-4">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto space-y-3 max-h-[400px]">
          {messages.length === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">
                Nenhuma mensagem ainda. Seja o primeiro a conversar!
              </p>
            </div>
          ) : (
            messages.map((message, index) => {
              const showDate = index === 0 || 
                formatDate(message.created_at) !== formatDate(messages[index - 1].created_at);
              
              return (
                <div key={message.id}>
                  {showDate && (
                    <div className="text-center text-xs text-muted-foreground py-2">
                      {formatDate(message.created_at)}
                    </div>
                  )}
                  <div className="flex space-x-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={getUserTypeColor(message.sender.user_type)}>
                          {message.sender.name || 'Usuário'}
                        </span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {getUserTypeLabel(message.sender.user_type)}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatTime(message.created_at)}
                        </span>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-3">
                        <p className="text-sm">{message.message}</p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Digite sua mensagem..."
            disabled={sending}
            className="flex-1"
          />
          <Button 
            type="submit" 
            disabled={!newMessage.trim() || sending}
            className="bg-gradient-to-r from-brain-500 to-cognitive-500"
          >
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
