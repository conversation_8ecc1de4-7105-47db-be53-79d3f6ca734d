-- SQL para corrigir políticas RLS e permitir cadastro de usuários
-- Execute este script no Supabase SQL Editor

-- 1. Desabilitar RLS temporariamente para debug
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 2. Remover todas as políticas existentes
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Masters can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Masters can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Allow public read access" ON profiles;
DROP POLICY IF EXISTS "Allow authenticated users to insert" ON profiles;

-- 3. Verificar se a foreign key está correta
DO $$ 
BEGIN
    -- Remove a foreign key constraint se existir
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'profiles_id_fkey' 
        AND table_name = 'profiles'
    ) THEN
        ALTER TABLE profiles DROP CONSTRAINT profiles_id_fkey;
        RAISE NOTICE 'Foreign key constraint profiles_id_fkey removed';
    END IF;
END $$;

-- 4. Recriar a constraint corretamente
ALTER TABLE profiles 
ADD CONSTRAINT profiles_id_fkey 
FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- 5. Reabilitar RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 6. Criar políticas mais permissivas para permitir cadastro
CREATE POLICY "Allow all operations for service role" ON profiles
FOR ALL USING (true);

CREATE POLICY "Users can view own profile" ON profiles
FOR SELECT USING (auth.uid() = id OR auth.role() = 'service_role');

CREATE POLICY "Users can insert own profile" ON profiles
FOR INSERT WITH CHECK (auth.uid() = id OR auth.role() = 'service_role');

CREATE POLICY "Users can update own profile" ON profiles
FOR UPDATE USING (auth.uid() = id OR auth.role() = 'service_role');

CREATE POLICY "Masters can view all profiles" ON profiles
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles p
    WHERE p.id = auth.uid() 
    AND p.user_type = 'master'
  ) OR auth.role() = 'service_role'
);

CREATE POLICY "Masters can update all profiles" ON profiles
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM profiles p
    WHERE p.id = auth.uid() 
    AND p.user_type = 'master'
  ) OR auth.role() = 'service_role'
);

-- 7. Criar ou atualizar a função trigger
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, user_type, status, created_at, name)
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'user_type', 'student'),
    CASE 
      WHEN COALESCE(new.raw_user_meta_data->>'user_type', 'student') = 'master' THEN 'approved'
      ELSE 'pending'
    END,
    now(),
    COALESCE(new.raw_user_meta_data->>'name', '')
  );
  RETURN new;
EXCEPTION
  WHEN others THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create profile for user %: %', new.id, SQLERRM;
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Recriar o trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 9. Garantir que as colunas necessárias existem
DO $$
BEGIN
    -- Adicionar colunas se não existirem
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'user_type') THEN
        ALTER TABLE profiles ADD COLUMN user_type TEXT DEFAULT 'student';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'status') THEN
        ALTER TABLE profiles ADD COLUMN status TEXT DEFAULT 'pending';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'course') THEN
        ALTER TABLE profiles ADD COLUMN course TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'name') THEN
        ALTER TABLE profiles ADD COLUMN name TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'phone') THEN
        ALTER TABLE profiles ADD COLUMN phone TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'semester') THEN
        ALTER TABLE profiles ADD COLUMN semester TEXT;
    END IF;
END $$;

-- 10. Testar se tudo está funcionando
DO $$
BEGIN
    RAISE NOTICE 'Testing profiles table access...';
    PERFORM COUNT(*) FROM profiles;
    RAISE NOTICE 'Profiles table is accessible!';
    RAISE NOTICE 'RLS policies fixed successfully!';
EXCEPTION
    WHEN others THEN
        RAISE WARNING 'Profiles table access test failed: %', SQLERRM;
END $$;
