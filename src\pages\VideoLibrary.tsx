import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Search,
  Play,
  Clock,
  BookOpen,
  Filter,
  Download,
  Heart,
  Share,
  ChevronRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";

const VideoLibrary = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");

  const videoCategories = [
    { id: "matematica", name: "Matemática Financeira", count: 24 },
    { id: "contabilidade", name: "Contabilidade", count: 18 },
    { id: "gestao", name: "Gestão de Projetos", count: 15 },
    { id: "marketing", name: "Marketing Digital", count: 12 },
    { id: "economia", name: "Economia", count: 8 },
  ];

  const featuredVideos = [
    {
      id: 1,
      title: "Introdução aos Juros Compostos",
      professor: "Prof. João Silva",
      duration: "45:30",
      views: 1240,
      category: "Matemática Financeira",
      thumbnail: "🧮",
      description:
        "Conceitos fundamentais e aplicações práticas de juros compostos",
      difficulty: "Iniciante",
      rating: 4.8,
    },
    {
      id: 2,
      title: "Demonstrações Contábeis - Parte 1",
      professor: "Profa. Ana Costa",
      duration: "52:15",
      views: 980,
      category: "Contabilidade",
      thumbnail: "📊",
      description: "Análise detalhada do Balanço Patrimonial",
      difficulty: "Intermediário",
      rating: 4.9,
    },
    {
      id: 3,
      title: "Metodologias Ágeis: Scrum na Prática",
      professor: "Prof. Carlos Mendes",
      duration: "38:45",
      views: 756,
      category: "Gestão de Projetos",
      thumbnail: "⚡",
      description: "Implementação do Scrum em projetos reais",
      difficulty: "Avançado",
      rating: 4.7,
    },
  ];

  const recentVideos = [
    {
      id: 4,
      title: "Análise de Investimentos",
      professor: "Prof. Maria Santos",
      duration: "35:20",
      uploadDate: "2 dias atrás",
      category: "Matemática Financeira",
      thumbnail: "💰",
    },
    {
      id: 5,
      title: "Marketing de Conteúdo",
      professor: "Prof. Pedro Lima",
      duration: "28:15",
      uploadDate: "1 semana atrás",
      category: "Marketing Digital",
      thumbnail: "📱",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header title="StudyHub" />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Biblioteca de Vídeos</h2>
          <p className="text-muted-foreground">
            Acesse toda a coleção de videoaulas organizadas por disciplina
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar vídeos, professores ou tópicos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar - Categories */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Categorias</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {videoCategories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-muted cursor-pointer"
                  >
                    <span className="text-sm">{category.name}</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="text-xs">
                        {category.count}
                      </Badge>
                      <ChevronRight className="h-3 w-3 text-muted-foreground" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Filtros</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Duração</h4>
                  <div className="space-y-2">
                    {["Menos de 30min", "30-60min", "Mais de 1h"].map(
                      (duration) => (
                        <label
                          key={duration}
                          className="flex items-center space-x-2 text-sm"
                        >
                          <input type="checkbox" className="rounded" />
                          <span>{duration}</span>
                        </label>
                      ),
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Dificuldade</h4>
                  <div className="space-y-2">
                    {["Iniciante", "Intermediário", "Avançado"].map((level) => (
                      <label
                        key={level}
                        className="flex items-center space-x-2 text-sm"
                      >
                        <input type="checkbox" className="rounded" />
                        <span>{level}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            <Tabs defaultValue="destaques" className="space-y-6">
              <TabsList>
                <TabsTrigger value="destaques">Destaques</TabsTrigger>
                <TabsTrigger value="recentes">Recentes</TabsTrigger>
                <TabsTrigger value="assistindo">Assistindo</TabsTrigger>
                <TabsTrigger value="favoritos">Favoritos</TabsTrigger>
              </TabsList>

              <TabsContent value="destaques">
                <div className="grid md:grid-cols-2 gap-6">
                  {featuredVideos.map((video) => (
                    <Card
                      key={video.id}
                      className="overflow-hidden hover:shadow-lg transition-shadow"
                    >
                      <div className="aspect-video bg-gradient-to-br from-brain-100 to-cognitive-100 flex items-center justify-center text-4xl">
                        {video.thumbnail}
                      </div>
                      <CardHeader>
                        <div className="space-y-2">
                          <Badge variant="outline">{video.category}</Badge>
                          <CardTitle className="text-lg line-clamp-2">
                            {video.title}
                          </CardTitle>
                          <CardDescription>{video.description}</CardDescription>
                        </div>
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>{video.professor}</span>
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{video.duration}</span>
                            </div>
                            <span>⭐ {video.rating}</span>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Badge
                              variant={
                                video.difficulty === "Iniciante"
                                  ? "secondary"
                                  : video.difficulty === "Intermediário"
                                    ? "default"
                                    : "destructive"
                              }
                            >
                              {video.difficulty}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {video.views} visualizações
                            </span>
                          </div>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="ghost">
                              <Heart className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="ghost">
                              <Share className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Assistir
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="recentes">
                <div className="space-y-4">
                  {recentVideos.map((video) => (
                    <Card key={video.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-4">
                          <div className="w-32 h-20 bg-gradient-to-br from-brain-100 to-cognitive-100 rounded flex items-center justify-center text-2xl">
                            {video.thumbnail}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold mb-1">
                              {video.title}
                            </h3>
                            <p className="text-sm text-muted-foreground mb-2">
                              {video.professor}
                            </p>
                            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                              <Badge variant="outline">{video.category}</Badge>
                              <span>⏱️ {video.duration}</span>
                              <span>📅 {video.uploadDate}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="ghost">
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Assistir
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="assistindo">
                <Card>
                  <CardContent className="p-8 text-center">
                    <Play className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">
                      Continue Assistindo
                    </h3>
                    <p className="text-muted-foreground">
                      Seus vídeos em progresso aparecerão aqui
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="favoritos">
                <Card>
                  <CardContent className="p-8 text-center">
                    <Heart className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">
                      Seus Favoritos
                    </h3>
                    <p className="text-muted-foreground">
                      Marque vídeos como favoritos para acessá-los rapidamente
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoLibrary;
