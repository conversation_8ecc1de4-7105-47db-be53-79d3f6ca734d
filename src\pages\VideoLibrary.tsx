import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Search,
  Play,
  Clock,
  BookOpen,
  Filter,
  Download,
  Heart,
  Share,
  ChevronRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import { SecureFileViewer } from "@/components/SecureFileViewer";
import {
  getPublishedContent,
  getStudentProgress,
  updateStudentProgress,
  getCurrentUser
} from "@/lib/supabase";

const VideoLibrary = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [videos, setVideos] = useState([]);
  const [progress, setProgress] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState(null);
  const [viewerModal, setViewerModal] = useState({
    isOpen: false,
    filePath: "",
    fileName: "",
    fileType: "",
    title: ""
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const user = await getCurrentUser();
      setCurrentUser(user);

      const [contentData, progressData] = await Promise.all([
        getPublishedContent(),
        user ? getStudentProgress(user.id) : []
      ]);

      // Filter only video content
      const videoContent = contentData.filter(content => content.type === 'video');
      setVideos(videoContent);
      setProgress(progressData);
    } catch (error) {
      console.error("Error loading video data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleWatchVideo = async (video) => {
    if (currentUser && video.file_url) {
      try {
        // Update progress when video is started
        await updateStudentProgress(currentUser.id, video.id, {
          progress_percentage: 0,
          completed: false
        });

        // Abrir visualizador seguro
        setViewerModal({
          isOpen: true,
          filePath: video.file_url, // Este é na verdade o caminho do arquivo
          fileName: video.title,
          fileType: video.type,
          title: video.title
        });
      } catch (error) {
        console.error("Error updating progress:", error);
      }
    } else {
      alert("Arquivo não disponível para visualização");
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return "N/A";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getVideoProgress = (videoId) => {
    return progress.find(p => p.content_id === videoId);
  };

  const videoCategories = videos.reduce((acc, video) => {
    const courseName = video.course?.name || 'Sem categoria';
    const existing = acc.find(cat => cat.name === courseName);
    if (existing) {
      existing.count++;
    } else {
      acc.push({
        id: courseName.toLowerCase().replace(/\s+/g, '-'),
        name: courseName,
        count: 1
      });
    }
    return acc;
  }, []);

  const featuredVideos = videos.slice(0, 6);
  const recentVideos = videos
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    .slice(0, 10);

  const watchingVideos = videos.filter(video => {
    const prog = getVideoProgress(video.id);
    return prog && prog.progress_percentage > 0 && !prog.completed;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
        <Header title="StudyHub" />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">Carregando vídeos...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header title="StudyHub" />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Biblioteca de Vídeos</h2>
          <p className="text-muted-foreground">
            Acesse toda a coleção de videoaulas organizadas por disciplina
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar vídeos, professores ou tópicos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar - Categories */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Categorias</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {videoCategories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-muted cursor-pointer"
                  >
                    <span className="text-sm">{category.name}</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="text-xs">
                        {category.count}
                      </Badge>
                      <ChevronRight className="h-3 w-3 text-muted-foreground" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Filtros</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Duração</h4>
                  <div className="space-y-2">
                    {["Menos de 30min", "30-60min", "Mais de 1h"].map(
                      (duration) => (
                        <label
                          key={duration}
                          className="flex items-center space-x-2 text-sm"
                        >
                          <input type="checkbox" className="rounded" />
                          <span>{duration}</span>
                        </label>
                      ),
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Dificuldade</h4>
                  <div className="space-y-2">
                    {["Iniciante", "Intermediário", "Avançado"].map((level) => (
                      <label
                        key={level}
                        className="flex items-center space-x-2 text-sm"
                      >
                        <input type="checkbox" className="rounded" />
                        <span>{level}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            <Tabs defaultValue="destaques" className="space-y-6">
              <TabsList>
                <TabsTrigger value="destaques">Destaques</TabsTrigger>
                <TabsTrigger value="recentes">Recentes</TabsTrigger>
                <TabsTrigger value="assistindo">Assistindo</TabsTrigger>
                <TabsTrigger value="favoritos">Favoritos</TabsTrigger>
              </TabsList>

              <TabsContent value="destaques">
                {featuredVideos.length > 0 ? (
                  <div className="grid md:grid-cols-2 gap-6">
                    {featuredVideos.map((video) => {
                      const videoProgress = getVideoProgress(video.id);
                      return (
                        <Card
                          key={video.id}
                          className="overflow-hidden hover:shadow-lg transition-shadow"
                        >
                          <div className="aspect-video bg-gradient-to-br from-brain-100 to-cognitive-100 flex items-center justify-center text-4xl relative">
                            🎥
                            {videoProgress && (
                              <div className="absolute bottom-2 left-2 right-2">
                                <div className="w-full bg-black/20 rounded-full h-1">
                                  <div
                                    className="bg-brain-500 h-1 rounded-full"
                                    style={{ width: `${videoProgress.progress_percentage}%` }}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                          <CardHeader>
                            <div className="space-y-2">
                              <Badge variant="outline">{video.course?.name || 'Curso'}</Badge>
                              <CardTitle className="text-lg line-clamp-2">
                                {video.title}
                              </CardTitle>
                              <CardDescription>{video.description || 'Conteúdo educativo'}</CardDescription>
                            </div>
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                              <span>StudyHub</span>
                              <div className="flex items-center space-x-4">
                                <div className="flex items-center space-x-1">
                                  <Clock className="h-3 w-3" />
                                  <span>{formatDuration(video.duration)}</span>
                                </div>
                                <span>{video.views || 0} visualizações</span>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <Badge variant="secondary">
                                  {video.type === 'video' ? 'Vídeo' : 'Conteúdo'}
                                </Badge>
                                {videoProgress && (
                                  <span className="text-xs text-muted-foreground">
                                    {videoProgress.completed ? 'Concluído' : `${videoProgress.progress_percentage}%`}
                                  </span>
                                )}
                              </div>
                              <div className="flex space-x-2">
                                <Button size="sm" variant="ghost">
                                  <Heart className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="ghost">
                                  <Share className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                                  onClick={() => handleWatchVideo(video)}
                                >
                                  <Play className="h-4 w-4 mr-1" />
                                  {videoProgress?.progress_percentage > 0 ? 'Continuar' : 'Assistir'}
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Play className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">
                        Nenhum vídeo disponível
                      </h3>
                      <p className="text-muted-foreground">
                        Novos vídeos serão adicionados em breve
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="recentes">
                {recentVideos.length > 0 ? (
                  <div className="space-y-4">
                    {recentVideos.map((video) => {
                      const videoProgress = getVideoProgress(video.id);
                      return (
                        <Card key={video.id}>
                          <CardContent className="p-4">
                            <div className="flex items-center space-x-4">
                              <div className="w-32 h-20 bg-gradient-to-br from-brain-100 to-cognitive-100 rounded flex items-center justify-center text-2xl relative">
                                🎥
                                {videoProgress && (
                                  <div className="absolute bottom-1 left-1 right-1">
                                    <div className="w-full bg-black/20 rounded-full h-1">
                                      <div
                                        className="bg-brain-500 h-1 rounded-full"
                                        style={{ width: `${videoProgress.progress_percentage}%` }}
                                      />
                                    </div>
                                  </div>
                                )}
                              </div>
                              <div className="flex-1">
                                <h3 className="font-semibold mb-1">
                                  {video.title}
                                </h3>
                                <p className="text-sm text-muted-foreground mb-2">
                                  StudyHub
                                </p>
                                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                  <Badge variant="outline">{video.course?.name || 'Curso'}</Badge>
                                  <span>⏱️ {formatDuration(video.duration)}</span>
                                  <span>📅 {new Date(video.created_at).toLocaleDateString('pt-BR')}</span>
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                <Button size="sm" variant="ghost">
                                  <Download className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                                  onClick={() => handleWatchVideo(video)}
                                >
                                  <Play className="h-4 w-4 mr-1" />
                                  {videoProgress?.progress_percentage > 0 ? 'Continuar' : 'Assistir'}
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Clock className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">
                        Nenhum vídeo recente
                      </h3>
                      <p className="text-muted-foreground">
                        Novos vídeos aparecerão aqui quando forem publicados
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="assistindo">
                {watchingVideos.length > 0 ? (
                  <div className="space-y-4">
                    {watchingVideos.map((video) => {
                      const videoProgress = getVideoProgress(video.id);
                      return (
                        <Card key={video.id}>
                          <CardContent className="p-4">
                            <div className="flex items-center space-x-4">
                              <div className="w-32 h-20 bg-gradient-to-br from-brain-100 to-cognitive-100 rounded flex items-center justify-center text-2xl relative">
                                🎥
                                <div className="absolute bottom-1 left-1 right-1">
                                  <div className="w-full bg-black/20 rounded-full h-1">
                                    <div
                                      className="bg-brain-500 h-1 rounded-full"
                                      style={{ width: `${videoProgress.progress_percentage}%` }}
                                    />
                                  </div>
                                </div>
                              </div>
                              <div className="flex-1">
                                <h3 className="font-semibold mb-1">
                                  {video.title}
                                </h3>
                                <p className="text-sm text-muted-foreground mb-2">
                                  Progresso: {videoProgress.progress_percentage}%
                                </p>
                                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                  <Badge variant="outline">{video.course?.name || 'Curso'}</Badge>
                                  <span>⏱️ {formatDuration(video.duration)}</span>
                                  <span>📅 Última vez: {new Date(videoProgress.last_accessed).toLocaleDateString('pt-BR')}</span>
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  size="sm"
                                  className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                                  onClick={() => handleWatchVideo(video)}
                                >
                                  <Play className="h-4 w-4 mr-1" />
                                  Continuar
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Play className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">
                        Continue Assistindo
                      </h3>
                      <p className="text-muted-foreground">
                        Seus vídeos em progresso aparecerão aqui
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="favoritos">
                <Card>
                  <CardContent className="p-8 text-center">
                    <Heart className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">
                      Seus Favoritos
                    </h3>
                    <p className="text-muted-foreground">
                      Marque vídeos como favoritos para acessá-los rapidamente
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Visualizador Seguro de Arquivos */}
      <SecureFileViewer
        isOpen={viewerModal.isOpen}
        onClose={() => setViewerModal(prev => ({ ...prev, isOpen: false }))}
        filePath={viewerModal.filePath}
        fileName={viewerModal.fileName}
        fileType={viewerModal.fileType}
        title={viewerModal.title}
      />
    </div>
  );
};

export default VideoLibrary;
