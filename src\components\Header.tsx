import { But<PERSON> } from "@/components/ui/button";
import { Brain, LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { signOut } from "@/lib/supabase";

interface HeaderProps {
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
}

const Header = ({ title = "StudyHub", subtitle, showBackButton = true }: HeaderProps) => {
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  return (
    <header className="border-b bg-card/95 backdrop-blur-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-brain-500 to-cognitive-500 rounded-lg flex items-center justify-center">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold">{title}</h1>
              {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {showBackButton && (
              <Button variant="ghost" onClick={() => navigate("/dashboard")}>
                Voltar ao Dashboard
              </Button>
            )}
            <Button variant="ghost" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Sair
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;