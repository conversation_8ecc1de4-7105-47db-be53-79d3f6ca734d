import React, { useState, useEffect } from "react";
import { Modal } from "./ui/modal";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { Badge } from "./ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "./ui/tabs";
import { 
  User, 
  GraduationCap, 
  Heart, 
  Settings, 
  Phone, 
  Mail, 
  Calendar,
  MapPin,
  BookOpen,
  Target,
  Clock,
  Bell
} from "lucide-react";
import { Profile, updateUserProfile } from "@/lib/supabase";

interface StudentProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  student: Profile | null;
  onUpdate: () => void;
}

export const StudentProfileModal: React.FC<StudentProfileModalProps> = ({
  isOpen,
  onClose,
  student,
  onUpdate,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Personal Info
    name: "",
    email: "",
    phone: "",
    course: "",
    semester: "",
    
    // Learning Info
    learning_goals: "",
    study_preferences: "",
    
    // Settings
    notifications_enabled: true,
  });

  const [learningGoals, setLearningGoals] = useState<string[]>([]);
  const [newGoal, setNewGoal] = useState("");

  useEffect(() => {
    if (student) {
      setFormData({
        name: student.name || "",
        email: student.email || "",
        phone: student.phone || "",
        course: student.course || "",
        semester: student.semester || "",
        learning_goals: student.learning_goals || "",
        study_preferences: student.study_preferences || "",
        notifications_enabled: student.notifications_enabled !== false,
      });

      // Parse learning goals if they exist
      if (student.learning_goals) {
        try {
          const goals = JSON.parse(student.learning_goals);
          setLearningGoals(Array.isArray(goals) ? goals : []);
        } catch {
          setLearningGoals([]);
        }
      }
    }
  }, [student]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addLearningGoal = () => {
    if (newGoal.trim() && !learningGoals.includes(newGoal.trim())) {
      setLearningGoals(prev => [...prev, newGoal.trim()]);
      setNewGoal("");
    }
  };

  const removeLearningGoal = (goal: string) => {
    setLearningGoals(prev => prev.filter(g => g !== goal));
  };

  const handleSave = async () => {
    if (!student) return;

    setIsLoading(true);
    try {
      const updateData = {
        ...formData,
        learning_goals: JSON.stringify(learningGoals),
      };

      await updateUserProfile(student.id, updateData);
      onUpdate();
      onClose();
    } catch (error) {
      console.error("Error updating student profile:", error);
      alert("Erro ao atualizar perfil do aluno");
    } finally {
      setIsLoading(false);
    }
  };

  if (!student) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Perfil do Aluno" size="xl">
      <div className="p-6">
        <Tabs defaultValue="personal" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="personal">
              <User className="h-4 w-4 mr-2" />
              Pessoal
            </TabsTrigger>
            <TabsTrigger value="academic">
              <GraduationCap className="h-4 w-4 mr-2" />
              Acadêmico
            </TabsTrigger>
            <TabsTrigger value="learning">
              <Target className="h-4 w-4 mr-2" />
              Aprendizado
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="h-4 w-4 mr-2" />
              Configurações
            </TabsTrigger>
          </TabsList>

          {/* Personal Information */}
          <TabsContent value="personal" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Nome Completo</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Nome completo do aluno"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500">Email não pode ser alterado</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Telefone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="(11) 99999-9999"
                />
              </div>
              <div className="space-y-2">
                <Label>Status da Conta</Label>
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant={student.status === "approved" ? "default" : 
                            student.status === "pending" ? "secondary" : "destructive"}
                  >
                    {student.status === "approved" ? "Aprovado" :
                     student.status === "pending" ? "Pendente" : "Suspenso"}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    Membro desde {new Date(student.created_at).toLocaleDateString("pt-BR")}
                  </span>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Academic Information */}
          <TabsContent value="academic" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="course">Curso</Label>
                <Input
                  id="course"
                  value={formData.course}
                  onChange={(e) => handleInputChange("course", e.target.value)}
                  placeholder="Ex: Administração"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="semester">Período/Semestre</Label>
                <Input
                  id="semester"
                  value={formData.semester}
                  onChange={(e) => handleInputChange("semester", e.target.value)}
                  placeholder="Ex: 5º período"
                />
              </div>
            </div>
          </TabsContent>

          {/* Learning Information */}
          <TabsContent value="learning" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label>Objetivos de Aprendizado</Label>
                <div className="flex space-x-2 mt-2">
                  <Input
                    value={newGoal}
                    onChange={(e) => setNewGoal(e.target.value)}
                    placeholder="Adicionar novo objetivo"
                    onKeyPress={(e) => e.key === "Enter" && addLearningGoal()}
                  />
                  <Button onClick={addLearningGoal} type="button">
                    Adicionar
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-3">
                  {learningGoals.map((goal, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="cursor-pointer"
                      onClick={() => removeLearningGoal(goal)}
                    >
                      {goal} ×
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="study_preferences">Preferências de Estudo</Label>
                <Textarea
                  id="study_preferences"
                  value={formData.study_preferences}
                  onChange={(e) => handleInputChange("study_preferences", e.target.value)}
                  placeholder="Descreva as preferências de estudo do aluno..."
                  rows={4}
                />
              </div>
            </div>
          </TabsContent>

          {/* Settings */}
          <TabsContent value="settings" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Notificações</Label>
                  <p className="text-sm text-gray-500">
                    Permitir que o aluno receba notificações
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.notifications_enabled}
                  onChange={(e) => handleInputChange("notifications_enabled", e.target.checked)}
                  className="h-4 w-4"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <div className="flex justify-end space-x-3 mt-8 pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? "Salvando..." : "Salvar Alterações"}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
