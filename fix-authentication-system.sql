-- COMPLETE FIX for authentication system
-- Run this in your Supabase SQL Editor to fix all issues

-- Step 1: Remove the automatic trigger that's causing conflicts
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Step 2: Clean up any duplicate or problematic profiles
-- First, let's see what we have
SELECT 
  p.id, 
  p.email, 
  p.user_type, 
  p.status, 
  p.created_at,
  CASE WHEN au.id IS NULL THEN 'ORPHANED' ELSE 'OK' END as auth_status
FROM profiles p
LEFT JOIN auth.users au ON p.id = au.id
ORDER BY p.created_at DESC;

-- Step 3: Delete orphaned profiles (profiles without auth users)
DELETE FROM profiles 
WHERE id NOT IN (SELECT id FROM auth.users);

-- Step 4: Fix foreign key constraints to prevent deletion issues
ALTER TABLE profiles 
DROP CONSTRAINT IF EXISTS profiles_approved_by_fkey;

ALTER TABLE profiles 
ADD CONSTRAINT profiles_approved_by_fkey 
FOREIGN KEY (approved_by) 
REFERENCES auth.users(id) 
ON DELETE SET NULL;

-- Step 5: Ensure the main constraint has CASCADE
ALTER TABLE profiles 
DROP CONSTRAINT IF EXISTS profiles_id_fkey;

ALTER TABLE profiles 
ADD CONSTRAINT profiles_id_fkey 
FOREIGN KEY (id) 
REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- Step 6: Create a BETTER trigger that only creates profiles when needed
-- This trigger will be more intelligent and avoid conflicts
CREATE OR REPLACE FUNCTION public.handle_new_user_smart()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create profile if it doesn't already exist
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
    INSERT INTO public.profiles (id, email, name, user_type, status)
    VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'name', ''),
      COALESCE(NEW.raw_user_meta_data->>'user_type', 'student'),
      CASE 
        WHEN COALESCE(NEW.raw_user_meta_data->>'user_type', 'student') = 'master' THEN 'approved'
        ELSE 'pending'
      END
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the new smart trigger
CREATE TRIGGER on_auth_user_created_smart
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user_smart();

-- Step 7: Update RLS policies to be more permissive for profile creation
DROP POLICY IF EXISTS "Allow profile creation" ON profiles;
DROP POLICY IF EXISTS "Allow profile creation during signup" ON profiles;

-- Create a more permissive policy for profile creation
CREATE POLICY "Allow profile creation for authenticated users" ON profiles 
  FOR INSERT WITH CHECK (
    auth.uid() = id OR 
    auth.uid() IS NOT NULL
  );

-- Step 8: Grant necessary permissions
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON profiles TO anon;

-- Step 9: Clean up any existing problematic data
-- Set approved_by to NULL for any users that no longer exist
UPDATE profiles 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL 
AND approved_by NOT IN (SELECT id FROM auth.users);

-- Step 10: Ensure all masters are approved
UPDATE profiles 
SET status = 'approved' 
WHERE user_type = 'master' AND status != 'approved';

-- Step 11: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_user_type ON profiles(user_type);
CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);

-- Step 12: Refresh schema cache
NOTIFY pgrst, 'reload schema';

-- Step 13: Show final state
SELECT 
  'FINAL STATE' as info,
  COUNT(*) as total_profiles,
  COUNT(CASE WHEN user_type = 'master' THEN 1 END) as masters,
  COUNT(CASE WHEN user_type = 'student' THEN 1 END) as students,
  COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
  COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending
FROM profiles;
