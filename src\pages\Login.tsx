import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Brain,
  Eye,
  EyeOff,
  LogIn,
  UserPlus,
  Shield,
  AlertCircle,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { signIn, signUp, getUserProfile } from "@/lib/supabase";
import {
  loginSchema,
  masterSignupSchema,
  LoginFormData,
  MasterSignupFormData,
} from "@/lib/validations";

const Login = () => {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSignup, setIsSignup] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const loginForm = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const signupForm = useForm<MasterSignupFormData>({
    resolver: zodResolver(masterSignupSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      masterCode: "",
    },
  });

  const handleLogin = async (data: LoginFormData) => {
    setIsLoading(true);
    setError("");

    try {
      const { data: authData, error: authError } = await signIn(data.email, data.password);

      if (authError) {
        console.error("Authentication error:", authError);
        setError(authError.message || "Erro ao fazer login. Verifique suas credenciais.");
        setIsLoading(false);
        return;
      }

      if (!authData.user) {
        setError("Usuário não encontrado. Verifique suas credenciais.");
        setIsLoading(false);
        return;
      }

      // Get user profile
      const profile = await getUserProfile(authData.user.id);
      
      if (!profile) {
        setError("Perfil de usuário não encontrado. Entre em contato com o suporte.");
        setIsLoading(false);
        return;
      }

      if (profile.status === "pending") {
        setError(
          "Sua conta ainda está aguardando aprovação. Você receberá um e-mail quando for aprovada."
        );
        setIsLoading(false);
        return;
      }

      if (profile.status === "suspended") {
        setError("Sua conta foi suspensa. Entre em contato com o suporte.");
        setIsLoading(false);
        return;
      }

      // Redirect based on user type
      if (profile.user_type === "master") {
        navigate("/master");
      } else {
        navigate("/dashboard");
      }
    } catch (err: any) {
      console.error("Login error:", err);
      setError(
        err.message || "Erro ao fazer login. Verifique suas credenciais."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (data: MasterSignupFormData) => {
    setIsLoading(true);
    setError("");

    // Verificar código master secreto
    if (data.masterCode !== "ADM2024#StudyHub@Master!") {
      setError("Código master inválido");
      setIsLoading(false);
      return;
    }

    try {
      await signUp(data.email, data.password, {
        name: data.name,
        user_type: "master",
      });

      setError("");
      alert("Conta master criada com sucesso! Você pode fazer login agora.");
      setIsSignup(false);
    } catch (err: any) {
      console.error("Error creating master account:", err);
      // Check if it's a profile creation error but auth user was created
      if (err.message && err.message.includes("learning_goals")) {
        alert("Conta criada com sucesso, mas houve um problema com o perfil. Você pode fazer login mesmo assim.");
        setIsSignup(false);
      } else {
        setError(err.message || "Erro ao criar conta master");
      }
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20 flex items-center justify-center">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-brain-500 to-cognitive-500 rounded-2xl flex items-center justify-center mb-4">
              <Brain className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold">StudyHub</h1>
            <p className="text-muted-foreground">
              {isSignup
                ? "Criar conta Master"
                : "Acesse sua plataforma de estudos"}
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">
                {isSignup ? "Criar Conta Master" : "Entrar"}
              </CardTitle>
              <CardDescription className="text-center">
                {isSignup
                  ? "Apenas para administradores da plataforma"
                  : "Entre com suas credenciais"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <Alert className="mb-6" variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {!isSignup ? (
                <form
                  onSubmit={loginForm.handleSubmit(handleLogin)}
                  className="space-y-4"
                >
                  <div className="space-y-2">
                    <Label htmlFor="email">E-mail</Label>
                    <Input
                      id="email"
                      type="email"
                      {...loginForm.register("email")}
                      placeholder="<EMAIL>"
                    />
                    {loginForm.formState.errors.email && (
                      <p className="text-sm text-destructive">
                        {loginForm.formState.errors.email.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">Senha</Label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        {...loginForm.register("password")}
                        placeholder="••••••••"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    {loginForm.formState.errors.password && (
                      <p className="text-sm text-destructive">
                        {loginForm.formState.errors.password.message}
                      </p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-brain-500 to-cognitive-500"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      "Entrando..."
                    ) : (
                      <>
                        <LogIn className="h-4 w-4 mr-2" />
                        Entrar
                      </>
                    )}
                  </Button>
                </form>
              ) : (
                <form
                  onSubmit={signupForm.handleSubmit(handleSignup)}
                  className="space-y-4"
                >
                  <div className="space-y-2">
                    <Label htmlFor="name">Nome Completo</Label>
                    <Input
                      id="name"
                      {...signupForm.register("name")}
                      placeholder="Seu nome completo"
                    />
                    {signupForm.formState.errors.name && (
                      <p className="text-sm text-destructive">
                        {signupForm.formState.errors.name.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-email">E-mail</Label>
                    <Input
                      id="signup-email"
                      type="email"
                      {...signupForm.register("email")}
                      placeholder="<EMAIL>"
                    />
                    {signupForm.formState.errors.email && (
                      <p className="text-sm text-destructive">
                        {signupForm.formState.errors.email.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-password">Senha</Label>
                    <div className="relative">
                      <Input
                        id="signup-password"
                        type={showPassword ? "text" : "password"}
                        {...signupForm.register("password")}
                        placeholder="••••••••"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    {signupForm.formState.errors.password && (
                      <p className="text-sm text-destructive">
                        {signupForm.formState.errors.password.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirmar Senha</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        {...signupForm.register("confirmPassword")}
                        placeholder="••••••••"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    {signupForm.formState.errors.confirmPassword && (
                      <p className="text-sm text-destructive">
                        {signupForm.formState.errors.confirmPassword.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="masterCode">Código Master</Label>
                    <Input
                      id="masterCode"
                      {...signupForm.register("masterCode")}
                      placeholder="Código de acesso administrativo"
                      type="password"
                    />
                    {signupForm.formState.errors.masterCode && (
                      <p className="text-sm text-destructive">
                        {signupForm.formState.errors.masterCode.message}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Código fornecido pelo administrador do sistema
                    </p>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-brain-500 to-cognitive-500"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      "Criando conta..."
                    ) : (
                      <>
                        <Shield className="h-4 w-4 mr-2" />
                        Criar Conta Master
                      </>
                    )}
                  </Button>
                </form>
              )}

              <div className="mt-6 text-center space-y-4">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      ou
                    </span>
                  </div>
                </div>

                {!isSignup ? (
                  <>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setIsSignup(true)}
                    >
                      <UserPlus className="h-4 w-4 mr-2" />
                      Criar Conta Master
                    </Button>
                    <Button
                      variant="ghost"
                      className="w-full"
                      onClick={() => navigate("/onboarding")}
                    >
                      Sou aluno - Fazer cadastro
                    </Button>


                  </>
                ) : (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setIsSignup(false)}
                  >
                    <LogIn className="h-4 w-4 mr-2" />
                    Já tenho conta
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
