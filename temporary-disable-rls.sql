-- TEMPORARY: Disable <PERSON><PERSON> for testing profile creation
-- Run this in Supabase SQL Editor to temporarily fix the issue
-- You can re-enable R<PERSON> later once everything is working

-- Disable <PERSON><PERSON> temporarily
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Grant permissions
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON profiles TO anon;

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';

-- NOTE: After testing, you can re-enable RLS with:
-- ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
-- Then run the fix-rls-policies.sql script to set up proper policies
