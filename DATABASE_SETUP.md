# Database Setup Instructions

## Quick Fix for Current Issues

The application is experiencing database schema issues. Follow these steps to resolve them:

### 1. Run the Database Setup Script

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `database-setup.sql`
4. Click "Run" to execute the script

### 2. Create Test Users

Since the SQL script cannot directly create auth users, you need to create them manually:

#### Option A: Through Supabase Dashboard
1. Go to Authentication > Users in your Supabase dashboard
2. Click "Add user"
3. Create these test users:
   - **Master User**: 
     - Email: `<EMAIL>`
     - Password: `master123`
     - User Metadata: `{"user_type": "master", "name": "Admin Master"}`
   - **Student User**:
     - Email: `<EMAIL>` 
     - Password: `aluno123`
     - User Metadata: `{"user_type": "student", "name": "Aluno Teste"}`

#### Option B: Through the Application
1. Start the application
2. Go to the login page
3. Use the "Criar conta Master" option to create a master account
4. Use the student registration flow to create a student account

### 3. Verify the Setup

After running the script and creating users:

1. Check that the `profiles` table exists with all required columns:
   - `id`, `email`, `name`, `user_type`, `status`, `created_at`, `updated_at`
   - `approved_by`, `course`, `semester`, `phone`, `learning_goals`
   - `study_preferences`, `notifications_enabled`

2. Verify that RLS policies are in place
3. Test login with both master and student accounts

### 4. Environment Variables

Make sure your `.env` file has the correct Supabase credentials:

```env
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## What the Script Does

1. **Creates the profiles table** with all required columns and constraints
2. **Sets up Row Level Security (RLS)** with appropriate policies
3. **Creates indexes** for better query performance
4. **Sets up a trigger** to automatically create profiles when users sign up
5. **Grants necessary permissions** for the application to work

## Troubleshooting

If you still see errors after running the script:

1. **Check the browser console** for specific error messages
2. **Verify RLS policies** are correctly set up
3. **Ensure the trigger function** is working by testing user creation
4. **Check that your Supabase project** has the correct permissions

## Manual Column Addition (if needed)

If you have an existing profiles table missing some columns, you can add them manually:

```sql
-- Add missing columns if they don't exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS user_type TEXT CHECK (user_type IN ('student', 'master', 'admin')) DEFAULT 'student';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS status TEXT CHECK (status IN ('pending', 'approved', 'suspended')) DEFAULT 'pending';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS learning_goals TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS study_preferences TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS notifications_enabled BOOLEAN DEFAULT true;
```
