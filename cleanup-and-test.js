// Cleanup and test script to fix authentication issues
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://azalziifkdybvaxijeqa.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF6YWx6aWlma2R5YnZheGlqZXFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2OTI0NzIsImV4cCI6MjA2NjI2ODQ3Mn0.9JsdWVkw0-7OYpegOHD76xedzbJx2QUEpgULeF2fJ_E';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function cleanupAndTest() {
  console.log('🧹 Starting cleanup and test process...');
  
  // Step 1: Check current state
  console.log('\n1. Checking current state...');
  try {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.log('❌ Error fetching profiles:', error.message);
    } else {
      console.log(`✅ Found ${profiles.length} profiles:`);
      profiles.forEach(profile => {
        console.log(`   - ${profile.email} (${profile.user_type || 'NO_TYPE'}) [${profile.status || 'NO_STATUS'}]`);
      });
    }
  } catch (err) {
    console.log('❌ Error:', err.message);
  }
  
  // Step 2: Test master login
  console.log('\n2. Testing master login...');
  const masterEmail = '<EMAIL>';
  const masterPassword = 'MAS123'; // Replace with actual password
  
  try {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: masterEmail,
      password: masterPassword
    });
    
    if (authError) {
      console.log('❌ Master login failed:', authError.message);
      
      if (authError.message.includes('Invalid login credentials')) {
        console.log('💡 Possible solutions:');
        console.log('   1. Check if email is confirmed in Supabase Auth dashboard');
        console.log('   2. Try resetting the password');
        console.log('   3. Check if user exists in auth.users table');
      }
    } else {
      console.log('✅ Master login successful!');
      console.log('   User ID:', authData.user.id);
      console.log('   Email confirmed:', authData.user.email_confirmed_at ? 'Yes' : 'No');
      
      // Check profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single();
      
      if (profileError) {
        console.log('❌ Profile not found:', profileError.message);
      } else {
        console.log('✅ Profile found:');
        console.log('   User type:', profile.user_type);
        console.log('   Status:', profile.status);
        console.log('   Name:', profile.name);
      }
      
      // Sign out
      await supabase.auth.signOut();
    }
  } catch (err) {
    console.log('❌ Unexpected error:', err.message);
  }
  
  // Step 3: Test student login
  console.log('\n3. Testing student login...');
  const studentEmail = '<EMAIL>';
  const studentPassword = 'aluno123'; // Replace with actual password
  
  try {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: studentEmail,
      password: studentPassword
    });
    
    if (authError) {
      console.log('❌ Student login failed:', authError.message);
    } else {
      console.log('✅ Student login successful!');
      
      // Check profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single();
      
      if (profileError) {
        console.log('❌ Profile not found:', profileError.message);
      } else {
        console.log('✅ Profile found:');
        console.log('   User type:', profile.user_type);
        console.log('   Status:', profile.status);
        console.log('   Name:', profile.name);
        
        if (profile.status === 'pending') {
          console.log('⚠️  Student is pending approval');
        }
      }
      
      // Sign out
      await supabase.auth.signOut();
    }
  } catch (err) {
    console.log('❌ Unexpected error:', err.message);
  }
  
  // Step 4: Recommendations
  console.log('\n4. Recommendations:');
  console.log('📋 Next steps to fix the issues:');
  console.log('   1. Run the fix-authentication-system.sql script in Supabase SQL Editor');
  console.log('   2. Delete problematic users from Supabase Auth dashboard');
  console.log('   3. Recreate users with proper email confirmation');
  console.log('   4. Ensure masters have status = "approved"');
  console.log('   5. Test login again');
  
  console.log('\n✅ Cleanup and test completed!');
}

cleanupAndTest();
