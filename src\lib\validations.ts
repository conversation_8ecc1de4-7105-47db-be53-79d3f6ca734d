import { z } from "zod";

// Login form validation
export const loginSchema = z.object({
  email: z.string().email({ message: "Email inválido" }),
  password: z.string().min(6, { message: "<PERSON><PERSON> deve ter pelo menos 6 caracteres" }),
});

export type LoginFormData = z.infer<typeof loginSchema>;

// Master signup validation
export const masterSignupSchema = z.object({
  name: z.string().min(3, { message: "Nome completo é obrigatório" }),
  email: z.string().email({ message: "Email inválido" }),
  password: z.string().min(6, { message: "Senha deve ter pelo menos 6 caracteres" }),
  confirmPassword: z.string(),
  masterCode: z.string().min(1, { message: "Código master é obrigatório" }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "<PERSON><PERSON> não conferem",
  path: ["confirmPassword"],
});

export type MasterSignupFormData = z.infer<typeof masterSignupSchema>;

// Student personal info validation
export const studentPersonalInfoSchema = z.object({
  name: z.string().min(3, { message: "Nome completo é obrigatório" }),
  email: z.string().email({ message: "Email inválido" }),
  course: z.string().min(2, { message: "Curso é obrigatório" }),
  semester: z.string().min(1, { message: "Período/semestre é obrigatório" }),
  phone: z.string().optional(),
  password: z.string().min(6, { message: "Senha deve ter pelo menos 6 caracteres" }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Senhas não conferem",
  path: ["confirmPassword"],
});

export type StudentPersonalInfoFormData = z.infer<typeof studentPersonalInfoSchema>;

// Learning goals validation
export const learningGoalsSchema = z.object({
  learningGoals: z.array(z.string()).min(1, { message: "Selecione pelo menos um objetivo de aprendizado" }),
});

export type LearningGoalsFormData = z.infer<typeof learningGoalsSchema>;

// Study preferences validation
export const studyPreferencesSchema = z.object({
  studyPreferences: z.string().min(1, { message: "Selecione uma preferência de estudo" }),
  notifications: z.boolean().default(true),
});

export type StudyPreferencesFormData = z.infer<typeof studyPreferencesSchema>;

// Helper function to format phone numbers
export function formatPhone(value: string): string {
  // Remove all non-numeric characters
  const numbers = value.replace(/\D/g, "");
  
  // Format as (XX) XXXXX-XXXX
  if (numbers.length <= 2) {
    return numbers;
  } else if (numbers.length <= 7) {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
  } else {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
  }
}
