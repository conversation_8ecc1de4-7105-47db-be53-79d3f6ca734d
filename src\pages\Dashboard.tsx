import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  Target,
  TrendingUp,
  Calendar,
  Award,
  BookOpen,
  Settings,
  Bell,
  Play,
  Video,
  MessageCircle,
  FileText,
  Users,
  LogOut,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  signOut,
  getCurrentUser,
  getUserProfile,
  getPublishedContent,
  getStudentProgress,
  getNotifications,
  getChatMessages,
  getPaymentPlans,
  subscribeToUserNotifications,
  subscribeToContent,
  subscribeToChatMessages
} from "@/lib/supabase";

const Dashboard = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [content, setContent] = useState([]);
  const [progress, setProgress] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [chatMessages, setChatMessages] = useState([]);
  const [paymentPlans, setPaymentPlans] = useState([]);
  const [stats, setStats] = useState({
    totalVideos: 0,
    totalMaterials: 0,
    unreadMessages: 0,
    unreadNotifications: 0,
    currentStreak: 0,
    completedLessons: 0,
    averageScore: 0,
  });
  
  useEffect(() => {
    checkUser();
  }, []);
  
  const checkUser = async () => {
    try {
      setIsLoading(true);
      const user = await getCurrentUser();
      
      if (!user) {
        // If not logged in, redirect to login
        navigate("/login");
        return;
      }
      
      // Check if user is a master
      const profile = await getUserProfile(user.id);
      
      if (!profile) {
        console.error("No profile found for user");
        navigate("/login");
        return;
      }
      
      // Ensure user_type is set
      if (!profile.user_type) {
        profile.user_type = 'student';
      }
      
      if (profile.user_type === "master") {
        // If master, redirect to master panel
        navigate("/master");
        return;
      }
      
      setUserData(profile);

      // Load student data
      await loadStudentData(profile.id);
    } catch (error) {
      console.error("Error checking user:", error);
      navigate("/login");
    } finally {
      setIsLoading(false);
    }
  };

  const loadStudentData = async (userId: string) => {
    try {
      // Load all student data in parallel
      const [
        contentData,
        progressData,
        notificationsData,
        chatData,
        paymentData
      ] = await Promise.all([
        getPublishedContent(),
        getStudentProgress(userId),
        getNotifications(userId),
        getChatMessages(),
        getPaymentPlans()
      ]);

      setContent(contentData);
      setProgress(progressData);
      setNotifications(notificationsData);
      setChatMessages(chatData);
      setPaymentPlans(paymentData.filter(plan => plan.student_id === userId));

      // Calculate stats
      const videoContent = contentData.filter(c => c.type === 'video');
      const documentContent = contentData.filter(c => c.type === 'document');
      const unreadNotifs = notificationsData.filter(n => !n.read);
      const completedProgress = progressData.filter(p => p.completed);
      const averageProgress = progressData.length > 0
        ? progressData.reduce((sum, p) => sum + p.progress_percentage, 0) / progressData.length
        : 0;

      setStats({
        totalVideos: videoContent.length,
        totalMaterials: documentContent.length,
        unreadMessages: 0, // Would need to implement message read status
        unreadNotifications: unreadNotifs.length,
        currentStreak: 7, // Would need to implement streak calculation
        completedLessons: completedProgress.length,
        averageScore: Math.round(averageProgress) / 10, // Convert to 0-10 scale
      });

      // Set up real-time subscriptions
      setupRealtimeSubscriptions(userId);
    } catch (error) {
      console.error("Error loading student data:", error);
    }
  };

  const setupRealtimeSubscriptions = (userId: string) => {
    // Subscribe to notifications
    const notificationSub = subscribeToUserNotifications(userId, (payload) => {
      if (payload.eventType === 'INSERT') {
        setNotifications(prev => [payload.new, ...prev]);
        setStats(prev => ({ ...prev, unreadNotifications: prev.unreadNotifications + 1 }));
      }
    });

    // Subscribe to content updates
    const contentSub = subscribeToContent((payload) => {
      if (payload.eventType === 'INSERT' && payload.new.status === 'published') {
        setContent(prev => [payload.new, ...prev]);
      } else if (payload.eventType === 'UPDATE') {
        setContent(prev => prev.map(c => c.id === payload.new.id ? payload.new : c));
      }
    });

    // Subscribe to chat messages
    const chatSub = subscribeToChatMessages((payload) => {
      if (payload.eventType === 'INSERT') {
        setChatMessages(prev => [...prev, payload.new]);
      }
    });

    // Cleanup subscriptions on unmount
    return () => {
      notificationSub?.unsubscribe();
      contentSub?.unsubscribe();
      chatSub?.unsubscribe();
    };
  };
  
  const handleLogout = async () => {
    try {
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  if (isLoading) {
    return <div className="min-h-screen flex items-center justify-center">Carregando...</div>;
  }

  if (!userData) {
    return <div className="min-h-screen flex items-center justify-center">Erro ao carregar dados do usuário</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      {/* Header */}
      <header className="border-b bg-card/95 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-brain-500 to-cognitive-500 rounded-lg flex items-center justify-center">
                <Brain className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-xl font-semibold">StudyHub</h1>
            </div>

            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate("/notifications")}
              >
                <Bell className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate("/settings")}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className="text-xs"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Sair
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Bem-vindo de volta, {userData.name || 'Aluno'}!</h2>
          <p className="text-muted-foreground">
            Pronto para continuar seus estudos hoje?
          </p>
        </div>

        {/* Quick Navigation */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => (window.location.href = "/live-classes")}
          >
            <CardContent className="p-4 text-center">
              <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <Users className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="font-semibold text-sm">Aulas ao Vivo</h3>
              <p className="text-xs text-muted-foreground">Em breve</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => (window.location.href = "/videos")}
          >
            <CardContent className="p-4 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <Video className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-sm">Vídeos</h3>
              <p className="text-xs text-muted-foreground">{stats.totalVideos} disponíveis</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => (window.location.href = "/materials")}
          >
            <CardContent className="p-4 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-sm">Materiais</h3>
              <p className="text-xs text-muted-foreground">{stats.totalMaterials} arquivos</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => (window.location.href = "/chat")}
          >
            <CardContent className="p-4 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <MessageCircle className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-sm">Chat</h3>
              <p className="text-xs text-muted-foreground">{stats.unreadMessages} não lidas</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Quick Start */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Play className="h-5 w-5 text-brain-500" />
                  <span>Próxima Aula</span>
                </CardTitle>
                <CardDescription>
                  Continue seus estudos onde parou
                </CardDescription>
              </CardHeader>
              <CardContent>
                {content.length > 0 ? (
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-brain-50 to-cognitive-50 rounded-lg border">
                    <div className="space-y-1">
                      <h3 className="font-semibold">
                        {content[0].title}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {content[0].description || "Conteúdo disponível para estudo"}
                      </p>
                      {content[0].duration && (
                        <p className="text-sm text-muted-foreground">
                          Duração: {Math.floor(content[0].duration / 60)} minutos
                        </p>
                      )}
                    </div>
                    <Button
                      className="bg-gradient-to-r from-brain-500 to-cognitive-500 hover:from-brain-600 hover:to-cognitive-600"
                      onClick={() => {
                        if (content[0].type === 'video') {
                          window.location.href = "/videos";
                        } else {
                          window.location.href = "/materials";
                        }
                      }}
                    >
                      {content[0].type === 'video' ? 'Assistir' : 'Acessar'}
                    </Button>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      Nenhum conteúdo disponível no momento
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Novos conteúdos serão adicionados em breve
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Progress Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-cognitive-500" />
                  <span>Progresso por Matéria</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {progress.length > 0 ? (
                  <div className="grid md:grid-cols-2 gap-4">
                    {progress.slice(0, 4).map((item, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>{item.content?.title || 'Conteúdo'}</span>
                          <span>{item.progress_percentage}%</span>
                        </div>
                        <Progress value={item.progress_percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      Nenhum progresso registrado ainda
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Comece a estudar para ver seu progresso aqui
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Atividades Recentes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {progress.length > 0 ? (
                    progress
                      .sort((a, b) => new Date(b.last_accessed).getTime() - new Date(a.last_accessed).getTime())
                      .slice(0, 3)
                      .map((activity, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 rounded-lg border"
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-brain-100 rounded-lg flex items-center justify-center">
                              <Target className="h-4 w-4 text-brain-600" />
                            </div>
                            <div>
                              <p className="font-medium">{activity.content?.title || 'Atividade'}</p>
                              <p className="text-sm text-muted-foreground">
                                {new Date(activity.last_accessed).toLocaleDateString('pt-BR')}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">
                              {activity.completed ? 'Concluído' : `${activity.progress_percentage}%`}
                            </p>
                            <p className="text-sm text-cognitive-600">
                              {activity.completed ? '✓' : '📚'}
                            </p>
                          </div>
                        </div>
                      ))
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">
                        Nenhuma atividade recente
                      </p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Suas atividades aparecerão aqui conforme você estuda
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Suas Estatísticas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Sequência Atual</span>
                  </div>
                  <span className="font-semibold">{stats.currentStreak} dias</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Award className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Conteúdos Concluídos</span>
                  </div>
                  <span className="font-semibold">{stats.completedLessons}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Progresso Médio</span>
                  </div>
                  <span className="font-semibold">{stats.averageScore.toFixed(1)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Study Resources */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="h-5 w-5" />
                  <span>Recursos de Estudo</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="ghost"
                  className="w-full justify-start"
                  onClick={() => (window.location.href = "/materials")}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Apostilas e Slides
                </Button>
                <Button
                  variant="ghost"
                  className="w-full justify-start"
                  onClick={() => (window.location.href = "/videos")}
                >
                  <Video className="h-4 w-4 mr-2" />
                  Biblioteca de Vídeos
                </Button>
                <Button
                  variant="ghost"
                  className="w-full justify-start"
                  onClick={() => (window.location.href = "/chat")}
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Chat da Turma
                </Button>
                <Button
                  variant="ghost"
                  className="w-full justify-start"
                  onClick={() => (window.location.href = "/live-classes")}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Aulas ao Vivo
                </Button>
              </CardContent>
            </Card>

            {/* Payment Status */}
            <Card>
              <CardHeader>
                <CardTitle>Status de Pagamento</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {paymentPlans.length > 0 ? (
                  paymentPlans.map((plan, index) => (
                    <div key={index} className="p-3 bg-muted/50 rounded-lg">
                      <p className="font-medium text-sm">{plan.course?.name || 'Curso'}</p>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-xs text-muted-foreground">
                          {plan.paid_installments}/{plan.installments} parcelas pagas
                        </p>
                        <span className={`text-xs px-2 py-1 rounded ${
                          plan.status === 'completed' ? 'bg-green-100 text-green-800' :
                          plan.status === 'active' ? 'bg-blue-100 text-blue-800' :
                          plan.status === 'overdue' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {plan.status === 'completed' ? 'Concluído' :
                           plan.status === 'active' ? 'Ativo' :
                           plan.status === 'overdue' ? 'Em Atraso' : 'Cancelado'}
                        </span>
                      </div>
                      {plan.next_due_date && plan.status === 'active' && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Próximo vencimento: {new Date(plan.next_due_date).toLocaleDateString('pt-BR')}
                        </p>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">
                      Nenhum plano de pagamento ativo
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
