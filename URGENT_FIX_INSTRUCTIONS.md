# 🚨 URGENT: Fix Database Schema Issues

## The Problem
Your profiles table is missing the `user_type` and `status` columns, which are causing the login and registration errors.

## Quick Fix (5 minutes)

### Step 1: Open Supabase SQL Editor
1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Navigate to **SQL Editor** (in the left sidebar)
3. Click **"New query"**

### Step 2: Run This SQL Script
Copy and paste this entire script into the SQL editor and click **"Run"**:

```sql
-- Add missing columns to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS user_type TEXT 
CHECK (user_type IN ('student', 'master', 'admin')) 
DEFAULT 'student';

ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS status TEXT 
CHECK (status IN ('pending', 'approved', 'suspended')) 
DEFAULT 'pending';

-- Add other missing columns
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS course TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS semester TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS phone TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS notifications_enabled BOOLEAN DEFAULT true;

-- Update existing records
UPDATE profiles 
SET user_type = 'student' 
WHERE user_type IS NULL;

UPDATE profiles 
SET status = 'pending' 
WHERE status IS NULL;

UPDATE profiles 
SET notifications_enabled = true 
WHERE notifications_enabled IS NULL;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_profiles_user_type ON profiles(user_type);
CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status);

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';
```

### Step 3: Verify the Fix
After running the script, run this test query to verify:

```sql
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;
```

You should see `user_type` and `status` columns in the results.

### Step 4: Restart Your Application
1. Stop your development server (Ctrl+C)
2. Start it again: `npm run dev`
3. Try creating a master account and logging in

## What This Fixes

✅ **Master Account Creation**: No more "status column not found" errors  
✅ **Student Registration**: Students can now register properly  
✅ **Login Issues**: Authentication will work correctly  
✅ **Dashboard Access**: Both student and master dashboards will load  

## Student Authorization Issue

For the "waiting for authorization" issue with students:

1. **Students need approval**: By default, students are created with `status = 'pending'`
2. **Masters approve students**: Masters can approve students through the master panel
3. **To test quickly**: You can manually approve a student by running:
   ```sql
   UPDATE profiles 
   SET status = 'approved' 
   WHERE email = '<EMAIL>';
   ```

## Test Users

After fixing the schema, you can create these test accounts:

- **Master**: `<EMAIL>` / `master123`
- **Student**: `<EMAIL>` / `aluno123`

The master account will be automatically approved, while student accounts need approval.

---

**⚠️ Important**: Run the SQL script first, then restart your application. The errors will disappear once the columns are added.
