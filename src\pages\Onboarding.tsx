import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useNavigate } from "react-router-dom";
import {
  Brain,
  Target,
  Users,
  TrendingUp,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  Eye,
  EyeOff,
  AlertCircle,
  Mail,
  Clock,
} from "lucide-react";
import { signUp } from "@/lib/supabase";
import {
  studentPersonalInfoSchema,
  learningGoalsSchema,
  studyPreferencesSchema,
  formatPhone,
  StudentPersonalInfoFormData,
  LearningGoalsFormData,
  StudyPreferencesFormData,
} from "@/lib/validations";

interface OnboardingData
  extends StudentPersonalInfoFormData,
    LearningGoalsFormData,
    StudyPreferencesFormData {
  isReturningStudent: boolean;
}

const Onboarding = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState("");
  const [isCompleted, setIsCompleted] = useState(false);

  // Forms para cada step
  const personalInfoForm = useForm<StudentPersonalInfoFormData>({
    resolver: zodResolver(studentPersonalInfoSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      email: "",
      course: "",
      semester: "",
      phone: "",
      password: "",
      confirmPassword: "",
    },
  });

  const learningGoalsForm = useForm<LearningGoalsFormData>({
    resolver: zodResolver(learningGoalsSchema),
    mode: "onChange",
    defaultValues: {
      learningGoals: [],
    },
  });

  const studyPreferencesForm = useForm<StudyPreferencesFormData>({
    resolver: zodResolver(studyPreferencesSchema),
    mode: "onChange",
    defaultValues: {
      studyPreferences: "",
      notifications: true,
    },
  });

  const [data, setData] = useState<OnboardingData>({
    name: "",
    email: "",
    course: "",
    semester: "",
    phone: "",
    password: "",
    confirmPassword: "",
    learningGoals: [],
    studyPreferences: "",
    notifications: true,
    isReturningStudent: false,
  });

  const totalSteps = 5; // Adicionamos o step de confirmação
  const progress = (currentStep / totalSteps) * 100;

  const handleNext = async () => {
    setError("");

    if (currentStep === 1) {
      setCurrentStep(2);
    } else if (currentStep === 2) {
      // Validate personal info form
      const result = await personalInfoForm.trigger();
      if (result) {
        const formData = personalInfoForm.getValues();
        setData((prev) => ({ ...prev, ...formData }));
        setCurrentStep(3);
      }
    } else if (currentStep === 3) {
      // Validate learning goals form
      const result = await learningGoalsForm.trigger();
      if (result) {
        const formData = learningGoalsForm.getValues();
        setData((prev) => ({ ...prev, ...formData }));
        setCurrentStep(4);
      }
    } else if (currentStep === 4) {
      // Validate study preferences form
      const result = await studyPreferencesForm.trigger();
      if (result) {
        const formData = studyPreferencesForm.getValues();
        setData((prev) => ({ ...prev, ...formData }));
        setCurrentStep(5);
      }
    } else if (currentStep === 5) {
      // Create student account
      await handleCreateStudentAccount();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      // If we're at the first step, navigate back to login
      navigate("/login");
    }
  };

  const handleCreateStudentAccount = async () => {
    setIsLoading(true);
    setError("");

    try {
      const allData = {
        ...data,
        ...personalInfoForm.getValues(),
        ...learningGoalsForm.getValues(),
        ...studyPreferencesForm.getValues(),
      };

      await signUp(allData.email, allData.password, {
        name: allData.name,
        course: allData.course,
        semester: allData.semester,
        phone: allData.phone,
        user_type: "student",
        learning_goals: allData.learningGoals,
        study_preferences: allData.studyPreferences,
        notifications_enabled: allData.notifications,
      });

      setIsCompleted(true);
    } catch (err: any) {
      setError(err.message || "Erro ao criar conta. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value);
    personalInfoForm.setValue("phone", formatted);
  };

  const toggleLearningGoal = (goal: string) => {
    const currentGoals = learningGoalsForm.getValues().learningGoals;
    const newGoals = currentGoals.includes(goal)
      ? currentGoals.filter((g) => g !== goal)
      : [...currentGoals, goal];

    learningGoalsForm.setValue("learningGoals", newGoals);
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <div className="mx-auto w-20 h-20 bg-gradient-to-br from-brain-500 to-cognitive-500 rounded-2xl flex items-center justify-center">
                <Brain className="h-10 w-10 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground mb-2">
                  Bem-vindo ao StudyHub
                </h1>
                <p className="text-lg text-muted-foreground max-w-md mx-auto">
                  Sua plataforma de estudos colaborativa com materiais, vídeos e
                  conexão com colegas
                </p>
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-brain-100 rounded-xl flex items-center justify-center">
                  <Target className="h-6 w-6 text-brain-600" />
                </div>
                <h3 className="font-semibold">Materiais de Estudo</h3>
                <p className="text-sm text-muted-foreground">
                  Acesse apostilas, exercícios e recursos personalizados para
                  seu curso
                </p>
              </div>

              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-cognitive-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-cognitive-600" />
                </div>
                <h3 className="font-semibold">Acompanhe Encontros</h3>
                <p className="text-sm text-muted-foreground">
                  Veja cronogramas de aulas e participe de encontros online
                </p>
              </div>

              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-accent/20 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-accent" />
                </div>
                <h3 className="font-semibold">Chat com Colegas</h3>
                <p className="text-sm text-muted-foreground">
                  Conecte-se e tire dúvidas com outros estudantes do seu curso
                </p>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h2 className="text-2xl font-bold">Conte-nos sobre você</h2>
              <p className="text-muted-foreground">
                Isso nos ajuda a personalizar sua experiência de estudos
              </p>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome Completo *</Label>
                  <Input
                    id="name"
                    {...personalInfoForm.register("name")}
                    placeholder="Digite seu nome completo"
                    className="h-12"
                  />
                  {personalInfoForm.formState.errors.name && (
                    <p className="text-sm text-destructive">
                      {personalInfoForm.formState.errors.name.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-mail *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...personalInfoForm.register("email")}
                    placeholder="<EMAIL>"
                    className="h-12"
                  />
                  {personalInfoForm.formState.errors.email && (
                    <p className="text-sm text-destructive">
                      {personalInfoForm.formState.errors.email.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="course">Curso *</Label>
                  <Input
                    id="course"
                    {...personalInfoForm.register("course")}
                    placeholder="Ex: Administração, Engenharia..."
                    className="h-12"
                  />
                  {personalInfoForm.formState.errors.course && (
                    <p className="text-sm text-destructive">
                      {personalInfoForm.formState.errors.course.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    {...personalInfoForm.register("phone")}
                    onChange={handlePhoneChange}
                    placeholder="(11) 99999-9999"
                    className="h-12"
                  />
                  {personalInfoForm.formState.errors.phone && (
                    <p className="text-sm text-destructive">
                      {personalInfoForm.formState.errors.phone.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <Label>Grupo de Estudo *</Label>
                <RadioGroup
                  value={personalInfoForm.watch("semester")}
                  onValueChange={(value) =>
                    personalInfoForm.setValue("semester", value)
                  }
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer">
                    <RadioGroupItem value="1-2" id="1-2" />
                    <label htmlFor="1-2" className="flex-1 cursor-pointer">
                      TCC - Criança e Adolescente
                    </label>
                  </div>
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer">
                    <RadioGroupItem value="3-5" id="3-5" />
                    <label htmlFor="3-5" className="flex-1 cursor-pointer">
                      TCC - Adultos
                    </label>
                  </div>
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer">
                    <RadioGroupItem value="6-8" id="6-8" />
                    <label htmlFor="6-8" className="flex-1 cursor-pointer">
                      Análise do Comportamento
                    </label>
                  </div>
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer">
                    <RadioGroupItem value="pos" id="pos" />
                    <label htmlFor="pos" className="flex-1 cursor-pointer">
                      Neuropsicologia
                    </label>
                  </div>
                </RadioGroup>
                {personalInfoForm.formState.errors.semester && (
                  <p className="text-sm text-destructive">
                    {personalInfoForm.formState.errors.semester.message}
                  </p>
                )}
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password">Senha *</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      {...personalInfoForm.register("password")}
                      placeholder="••••••••"
                      className="h-12"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {personalInfoForm.formState.errors.password && (
                    <p className="text-sm text-destructive">
                      {personalInfoForm.formState.errors.password.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirmar Senha *</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      {...personalInfoForm.register("confirmPassword")}
                      placeholder="••••••••"
                      className="h-12"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {personalInfoForm.formState.errors.confirmPassword && (
                    <p className="text-sm text-destructive">
                      {
                        personalInfoForm.formState.errors.confirmPassword
                          .message
                      }
                    </p>
                  )}
                </div>
              </div>
            </form>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h2 className="text-2xl font-bold">
                Quais são seus objetivos de aprendizado?
              </h2>
              <p className="text-muted-foreground">
                Selecione todos que se aplicam para personalizar seu conteúdo
              </p>
            </div>

            <div className="space-y-4">
              {[
                {
                  id: "materials",
                  label: "Acessar Materiais",
                  description: "Visualizar apostilas, slides e documentos do curso",
                },
                {
                  id: "videos",
                  label: "Assistir Videoaulas",
                  description:
                    "Acompanhar aulas gravadas e conteúdo audiovisual",
                },
                {
                  id: "meetings",
                  label: "Participar de Encontros",
                  description:
                    "Acompanhar cronograma e participar de aulas online",
                },
                {
                  id: "chat",
                  label: "Interagir com Colegas",
                  description: "Trocar ideias e tirar dúvidas no chat do grupo",
                },
                {
                  id: "exercises",
                  label: "Resolver Exercícios",
                  description: "Praticar com listas de exercícios e atividades",
                },
                {
                  id: "progress",
                  label: "Acompanhar Progresso",
                  description: "Monitorar desempenho e evolução nos estudos",
                },
              ].map((goal) => (
                <div
                  key={goal.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    learningGoalsForm.watch("learningGoals").includes(goal.id)
                      ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                      : "border-border hover:bg-muted/50"
                  }`}
                  onClick={() => toggleLearningGoal(goal.id)}
                >
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      checked={learningGoalsForm
                        .watch("learningGoals")
                        .includes(goal.id)}
                      onChange={() => toggleLearningGoal(goal.id)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold">{goal.label}</h3>
                      <p className="text-sm text-muted-foreground">
                        {goal.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              {learningGoalsForm.formState.errors.learningGoals && (
                <p className="text-sm text-destructive">
                  {learningGoalsForm.formState.errors.learningGoals.message}
                </p>
              )}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h2 className="text-2xl font-bold">Preferências de Estudo</h2>
              <p className="text-muted-foreground">
                Configure como prefere acessar o conteúdo
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-3">
                <Label>Como prefere organizar seus estudos?</Label>
                <RadioGroup
                  value={studyPreferencesForm.watch("studyPreferences")}
                  onValueChange={(value) =>
                    studyPreferencesForm.setValue("studyPreferences", value)
                  }
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer">
                    <RadioGroupItem value="daily" id="daily" />
                    <label htmlFor="daily" className="flex-1 cursor-pointer">
                      <span className="font-medium">Estudo Diário</span>
                      <p className="text-sm text-muted-foreground">
                        Pequenas sessões todos os dias
                      </p>
                    </label>
                  </div>
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer">
                    <RadioGroupItem value="intensive" id="intensive" />
                    <label
                      htmlFor="intensive"
                      className="flex-1 cursor-pointer"
                    >
                      <span className="font-medium">Sessões Intensas</span>
                      <p className="text-sm text-muted-foreground">
                        Períodos mais longos, menos frequentes
                      </p>
                    </label>
                  </div>
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer">
                    <RadioGroupItem value="flexible" id="flexible" />
                    <label htmlFor="flexible" className="flex-1 cursor-pointer">
                      <span className="font-medium">Flexível</span>
                      <p className="text-sm text-muted-foreground">
                        Vou decidir conforme minha disponibilidade
                      </p>
                    </label>
                  </div>
                </RadioGroup>
                {studyPreferencesForm.formState.errors.studyPreferences && (
                  <p className="text-sm text-destructive">
                    {
                      studyPreferencesForm.formState.errors.studyPreferences
                        .message
                    }
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-3 p-4 rounded-lg border">
                <Checkbox
                  checked={studyPreferencesForm.watch("notifications")}
                  onCheckedChange={(checked) =>
                    studyPreferencesForm.setValue("notifications", checked)
                  }
                  id="notifications"
                />
                <label
                  htmlFor="notifications"
                  className="flex-1 cursor-pointer"
                >
                  <span className="font-medium">Ativar notificações</span>
                  <p className="text-sm text-muted-foreground">
                    Receba lembretes de encontros, novos materiais e mensagens
                  </p>
                </label>
              </div>
            </div>
          </div>
        );

      case 5:
        if (isCompleted) {
          return (
            <div className="space-y-8 text-center">
              <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>

              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-green-800">
                  Cadastro Enviado com Sucesso!
                </h2>
                <div className="max-w-md mx-auto space-y-3">
                  <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                    <Mail className="h-5 w-5 text-blue-600" />
                    <div className="text-left">
                      <p className="font-medium text-blue-900">
                        E-mail de confirmação enviado
                      </p>
                      <p className="text-sm text-blue-700">
                        Verifique sua caixa de entrada
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                    <Clock className="h-5 w-5 text-yellow-600" />
                    <div className="text-left">
                      <p className="font-medium text-yellow-900">
                        Aguardando aprovação
                      </p>
                      <p className="text-sm text-yellow-700">
                        O Master irá revisar e aprovar sua conta
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-muted rounded-lg max-w-md mx-auto">
                  <p className="text-sm text-muted-foreground text-center">
                    Você receberá um e-mail com suas credenciais de acesso
                    quando sua conta for aprovada. Isso geralmente acontece em
                    até 24 horas.
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={() => navigate("/login")}
                  className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                >
                  Ir para Login
                </Button>
                <br />
                <Button variant="ghost" onClick={() => navigate("/")}>
                  Voltar ao Início
                </Button>
              </div>
            </div>
          );
        }

        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h2 className="text-2xl font-bold">Confirme suas informações</h2>
              <p className="text-muted-foreground">
                Revise seus dados antes de finalizar o cadastro
              </p>
            </div>

            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h3 className="font-semibold">Informações Pessoais</h3>
                  <div className="p-3 bg-muted/50 rounded-lg space-y-2 text-sm">
                    <p>
                      <strong>Nome:</strong> {personalInfoForm.getValues().name}
                    </p>
                    <p>
                      <strong>E-mail:</strong>{" "}
                      {personalInfoForm.getValues().email}
                    </p>
                    <p>
                      <strong>Curso:</strong>{" "}
                      {personalInfoForm.getValues().course}
                    </p>
                    <p>
                      <strong>Período:</strong>{" "}
                      {personalInfoForm.getValues().semester}
                    </p>
                    {personalInfoForm.getValues().phone && (
                      <p>
                        <strong>Telefone:</strong>{" "}
                        {personalInfoForm.getValues().phone}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold">Preferências</h3>
                  <div className="p-3 bg-muted/50 rounded-lg space-y-2 text-sm">
                    <p>
                      <strong>Objetivos:</strong>{" "}
                      {learningGoalsForm.getValues().learningGoals.length}{" "}
                      selecionados
                    </p>
                    <p>
                      <strong>Estudo:</strong>{" "}
                      {studyPreferencesForm.getValues().studyPreferences ===
                      "daily"
                        ? "Diário"
                        : studyPreferencesForm.getValues().studyPreferences ===
                            "intensive"
                          ? "Intenso"
                          : "Flexível"}
                    </p>
                    <p>
                      <strong>Notificações:</strong>{" "}
                      {studyPreferencesForm.getValues().notifications
                        ? "Ativadas"
                        : "Desativadas"}
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-cognitive-50 rounded-lg border border-cognitive-200">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-cognitive-600 mt-0.5" />
                  <div className="space-y-1">
                    <p className="font-medium text-cognitive-900">
                      Importante - Processo de Aprovação
                    </p>
                    <p className="text-sm text-cognitive-700">
                      Sua conta será criada, mas você precisará aguardar a
                      aprovação de um Master para acessar a plataforma. Você
                      receberá um e-mail quando sua conta for aprovada.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return true;
      case 2:
        return (
          personalInfoForm.formState.isValid &&
          personalInfoForm.watch("name") &&
          personalInfoForm.watch("email") &&
          personalInfoForm.watch("course") &&
          personalInfoForm.watch("semester") &&
          personalInfoForm.watch("password") &&
          personalInfoForm.watch("confirmPassword")
        );
      case 3:
        return learningGoalsForm.watch("learningGoals").length > 0;
      case 4:
        return studyPreferencesForm.watch("studyPreferences") !== "";
      case 5:
        return !isCompleted;
      default:
        return false;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/30 to-cognitive-50/30">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Progress Header */}
          <div className="mb-8 space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">
                Passo {currentStep} de {totalSteps}
              </span>
              <span className="text-sm font-medium text-muted-foreground">
                {Math.round(progress)}% Concluído
              </span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Main Content */}
          <Card className="border-0 shadow-xl bg-card/95 backdrop-blur-sm">
            <CardContent className="p-8">{renderStep()}</CardContent>
          </Card>

          {/* Navigation */}
          <div className="flex justify-between mt-8">
            <Button
              variant="outline"
              onClick={handlePrevious}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Anterior</span>
            </Button>

            <Button
              onClick={handleNext}
              disabled={!canProceed() || isLoading}
              className="flex items-center space-x-2 bg-gradient-to-r from-brain-500 to-cognitive-500 hover:from-brain-600 hover:to-cognitive-600"
            >
              <span>
                {isLoading
                  ? "Criando conta..."
                  : currentStep === 5 && !isCompleted
                    ? "Finalizar Cadastro"
                    : currentStep === totalSteps
                      ? "Começar"
                      : "Continuar"}
              </span>
              {!isLoading && <ArrowRight className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
