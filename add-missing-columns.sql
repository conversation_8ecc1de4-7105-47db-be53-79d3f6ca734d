-- Add missing columns to the profiles table
-- Run this in your Supabase SQL editor

-- Add user_type column
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS user_type TEXT 
CHECK (user_type IN ('student', 'master', 'admin')) 
DEFAULT 'student';

-- Add status column  
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS status TEXT 
CHECK (status IN ('pending', 'approved', 'suspended')) 
DEFAULT 'pending';

-- Add other potentially missing columns
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS course TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS semester TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS phone TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS notifications_enabled BOOLEAN DEFAULT true;

-- Update existing records to have default values
UPDATE profiles 
SET user_type = 'student' 
WHERE user_type IS NULL;

UPDATE profiles 
SET status = 'pending' 
WHERE status IS NULL;

UPDATE profiles 
SET notifications_enabled = true 
WHERE notifications_enabled IS NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_user_type ON profiles(user_type);
CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status);

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
