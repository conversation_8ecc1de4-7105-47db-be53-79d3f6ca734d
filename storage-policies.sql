-- Run this in Supabase SQL Editor to fix CORS and access issues

-- 1. First, make sure the bucket exists and is properly configured
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('private-content', 'private-content', false, 5368709120, NULL)
ON CONFLICT (id) DO UPDATE SET
  public = false,
  file_size_limit = 5368709120;

-- 2. Remove existing policies to avoid conflicts
DROP POLICY IF EXISTS "Masters can upload files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can view files" ON storage.objects;
DROP POLICY IF EXISTS "Masters can delete files" ON storage.objects;
DROP POLICY IF EXISTS "Masters can update files" ON storage.objects;

-- 3. Create comprehensive storage policies

-- Allow masters to upload files
CREATE POLICY "Masters can upload files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'private-content' AND
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.user_type = 'master'
  )
);

-- Allow authenticated users to view/download files
CREATE POLICY "Authenticated users can view files" ON storage.objects
FOR SELECT USING (
  bucket_id = 'private-content' AND
  auth.role() = 'authenticated'
);

-- Allow masters to delete files
CREATE POLICY "Masters can delete files" ON storage.objects
FOR DELETE USING (
  bucket_id = 'private-content' AND
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.user_type = 'master'
  )
);

-- Allow masters to update files
CREATE POLICY "Masters can update files" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'private-content' AND
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.user_type = 'master'
  )
);

-- 4. Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 5. Grant necessary permissions
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;
