// Script to add missing columns to the profiles table
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://azalziifkdybvaxijeqa.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF6YWx6aWlma2R5YnZheGlqZXFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2OTI0NzIsImV4cCI6MjA2NjI2ODQ3Mn0.9JsdWVkw0-7OYpegOHD76xedzbJx2QUEpgULeF2fJ_E';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function addMissingColumns() {
  console.log('🔧 Adding missing columns to profiles table...');
  
  const sqlCommands = [
    // Add user_type column
    `ALTER TABLE profiles 
     ADD COLUMN IF NOT EXISTS user_type TEXT 
     CHECK (user_type IN ('student', 'master', 'admin')) 
     DEFAULT 'student'`,
    
    // Add status column
    `ALTER TABLE profiles 
     ADD COLUMN IF NOT EXISTS status TEXT 
     CHECK (status IN ('pending', 'approved', 'suspended')) 
     DEFAULT 'pending'`,
    
    // Add other missing columns
    `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ`,
    `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id)`,
    `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS course TEXT`,
    `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS semester TEXT`,
    `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS phone TEXT`,
    `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS notifications_enabled BOOLEAN DEFAULT true`,
    
    // Update existing records
    `UPDATE profiles SET user_type = 'student' WHERE user_type IS NULL`,
    `UPDATE profiles SET status = 'pending' WHERE status IS NULL`,
    `UPDATE profiles SET notifications_enabled = true WHERE notifications_enabled IS NULL`,
    
    // Create indexes
    `CREATE INDEX IF NOT EXISTS idx_profiles_user_type ON profiles(user_type)`,
    `CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status)`
  ];
  
  for (let i = 0; i < sqlCommands.length; i++) {
    const sql = sqlCommands[i];
    console.log(`\n${i + 1}. Executing: ${sql.split('\n')[0]}...`);
    
    try {
      const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
      
      if (error) {
        console.error(`❌ Error: ${error.message}`);
        // Try alternative approach for some commands
        if (sql.includes('ALTER TABLE')) {
          console.log('⚠️  Note: You may need to run this SQL command manually in Supabase SQL Editor');
        }
      } else {
        console.log('✅ Success');
      }
    } catch (err) {
      console.error(`❌ Exception: ${err.message}`);
      console.log('⚠️  Note: You may need to run this SQL command manually in Supabase SQL Editor');
    }
  }
  
  console.log('\n🔍 Testing the changes...');
  
  // Test if columns were added
  const testColumns = ['user_type', 'status'];
  for (const column of testColumns) {
    try {
      const { error } = await supabase
        .from('profiles')
        .select(column)
        .limit(1);
      
      if (error) {
        console.log(`❌ Column '${column}' still missing: ${error.message}`);
      } else {
        console.log(`✅ Column '${column}' now exists`);
      }
    } catch (err) {
      console.log(`❌ Column '${column}' test failed: ${err.message}`);
    }
  }
}

// Also create the exec_sql function if it doesn't exist
async function createExecSqlFunction() {
  console.log('🔧 Creating exec_sql function...');
  
  const functionSql = `
    CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
    RETURNS void AS $$
    BEGIN
      EXECUTE sql_query;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
  `;
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql_query: functionSql });
    if (error) {
      console.log('⚠️  exec_sql function may not exist. Please run the SQL commands manually.');
    } else {
      console.log('✅ exec_sql function created');
    }
  } catch (err) {
    console.log('⚠️  exec_sql function may not exist. Please run the SQL commands manually.');
  }
}

async function main() {
  await createExecSqlFunction();
  await addMissingColumns();
  
  console.log('\n📋 Manual Steps Required:');
  console.log('1. Go to your Supabase project dashboard');
  console.log('2. Navigate to SQL Editor');
  console.log('3. Copy and paste the contents of add-missing-columns.sql');
  console.log('4. Click "Run" to execute the script');
  console.log('5. Restart your application');
}

main();
