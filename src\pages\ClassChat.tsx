import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Brain,
  Send,
  Search,
  Users,
  MessageCircle,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Pin,
  Settings,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";

const ClassChat = () => {
  const navigate = useNavigate();
  const [message, setMessage] = useState("");
  const [selectedChannel, setSelectedChannel] = useState("geral");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, []);

  const channels = [
    {
      id: "geral",
      name: "💬 Geral",
      description: "Discussões gerais da turma",
      members: 45,
      unread: 3,
    },
    {
      id: "matematica",
      name: "🧮 Matemática Financeira",
      description: "Dúvidas e discussões sobre a matéria",
      members: 42,
      unread: 1,
    },
    {
      id: "contabilidade",
      name: "📊 Contabilidade",
      description: "Apoio em contabilidade geral",
      members: 38,
      unread: 0,
    },
    {
      id: "projetos",
      name: "⚡ Gestão de Projetos",
      description: "Metodologias ágeis e práticas",
      members: 35,
      unread: 5,
    },
    {
      id: "avisos",
      name: "📢 Avisos",
      description: "Comunicados importantes",
      members: 45,
      unread: 0,
    },
  ];

  const messages = [
    {
      id: 1,
      user: "Maria Santos",
      avatar: "MS",
      message:
        "Pessoal, alguém pode me ajudar com o exercício 5 da lista de juros compostos?",
      time: "09:15",
      isMe: false,
      reactions: ["👍", "❤️"],
    },
    {
      id: 2,
      user: "Pedro Lima",
      avatar: "PL",
      message: "Oi Maria! Qual parte específica você está com dúvida?",
      time: "09:17",
      isMe: false,
      reactions: [],
    },
    {
      id: 3,
      user: "Você",
      avatar: "EU",
      message:
        "Também estava com dúvida nesse exercício. A fórmula que estou usando é FV = PV × (1 + i)^n",
      time: "09:18",
      isMe: true,
      reactions: ["👍"],
    },
    {
      id: 4,
      user: "Ana Silva",
      avatar: "AS",
      message:
        "Isso mesmo! Lembrem de converter a taxa para decimal antes de calcular",
      time: "09:20",
      isMe: false,
      reactions: ["💡"],
    },
    {
      id: 5,
      user: "Prof. João Silva",
      avatar: "JS",
      message:
        "Excelente discussão! Vou compartilhar um material complementar sobre esse tópico na próxima aula",
      time: "09:25",
      isMe: false,
      reactions: ["🙏", "📚", "👏"],
      isTeacher: true,
    },
    {
      id: 6,
      user: "Carlos Mendes",
      avatar: "CM",
      message: "Professor, vai ter aula de revisão antes da prova?",
      time: "09:30",
      isMe: false,
      reactions: [],
    },
  ];

  const onlineUsers = [
    { name: "Maria Santos", avatar: "MS", status: "online" },
    { name: "Pedro Lima", avatar: "PL", status: "online" },
    { name: "Ana Silva", avatar: "AS", status: "online" },
    {
      name: "Prof. João Silva",
      avatar: "JS",
      status: "online",
      isTeacher: true,
    },
    { name: "Carlos Mendes", avatar: "CM", status: "away" },
    { name: "Julia Costa", avatar: "JC", status: "offline" },
  ];

  const sendMessage = () => {
    if (message.trim()) {
      // Aqui seria enviada a mensagem
      setMessage("");
    }
  };

  const currentChannel = channels.find((ch) => ch.id === selectedChannel);

  return (
    <div className="min-h-screen bg-background">
      <Header title="StudyHub" />

      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar - Channels */}
        <div className="w-80 border-r bg-card flex flex-col">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold mb-4">Chat da Turma</h2>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Buscar conversas..." className="pl-10" />
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-2">
              {channels.map((channel) => (
                <div
                  key={channel.id}
                  onClick={() => setSelectedChannel(channel.id)}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedChannel === channel.id
                      ? "bg-primary/10 border border-primary/20"
                      : "hover:bg-muted/50"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium text-sm">{channel.name}</h3>
                        {channel.unread > 0 && (
                          <Badge
                            variant="destructive"
                            className="h-5 w-5 p-0 text-xs flex items-center justify-center"
                          >
                            {channel.unread}
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {channel.description}
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Users className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          {channel.members} membros
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Header */}
          <div className="p-4 border-b bg-card">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{currentChannel?.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {currentChannel?.members} membros •{" "}
                  {currentChannel?.description}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="ghost">
                  <Phone className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Video className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Pin className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((msg) => (
              <div
                key={msg.id}
                className={`flex space-x-3 ${msg.isMe ? "flex-row-reverse space-x-reverse" : ""}`}
              >
                <Avatar className="h-8 w-8">
                  <AvatarFallback
                    className={
                      msg.isTeacher ? "bg-brain-100 text-brain-700" : "bg-muted"
                    }
                  >
                    {msg.avatar}
                  </AvatarFallback>
                </Avatar>
                <div
                  className={`flex-1 max-w-xs md:max-w-md ${msg.isMe ? "text-right" : ""}`}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    {!msg.isMe && (
                      <>
                        <span
                          className={`text-sm font-medium ${msg.isTeacher ? "text-brain-700" : ""}`}
                        >
                          {msg.user}
                          {msg.isTeacher && (
                            <Badge variant="secondary" className="ml-1 text-xs">
                              Prof
                            </Badge>
                          )}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {msg.time}
                        </span>
                      </>
                    )}
                    {msg.isMe && (
                      <>
                        <span className="text-xs text-muted-foreground">
                          {msg.time}
                        </span>
                        <span className="text-sm font-medium">{msg.user}</span>
                      </>
                    )}
                  </div>
                  <div
                    className={`p-3 rounded-lg text-sm ${
                      msg.isMe
                        ? "bg-primary text-primary-foreground"
                        : msg.isTeacher
                          ? "bg-brain-50 border border-brain-200"
                          : "bg-muted"
                    }`}
                  >
                    {msg.message}
                  </div>
                  {msg.reactions.length > 0 && (
                    <div className="flex space-x-1 mt-1">
                      {msg.reactions.map((reaction, index) => (
                        <span
                          key={index}
                          className="text-xs bg-background border rounded px-1"
                        >
                          {reaction}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="p-4 border-t bg-card">
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="ghost">
                <Paperclip className="h-4 w-4" />
              </Button>
              <div className="flex-1 relative">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder={`Enviar mensagem para ${currentChannel?.name}`}
                  onKeyPress={(e) => e.key === "Enter" && sendMessage()}
                  className="pr-10"
                />
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2"
                >
                  <Smile className="h-4 w-4" />
                </Button>
              </div>
              <Button
                onClick={sendMessage}
                disabled={!message.trim()}
                className="bg-gradient-to-r from-brain-500 to-cognitive-500"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Online Users */}
        <div className="w-64 border-l bg-card">
          <div className="p-4 border-b">
            <h3 className="font-semibold">Usuários Online</h3>
            <p className="text-sm text-muted-foreground">
              {onlineUsers.filter((u) => u.status === "online").length} online
            </p>
          </div>
          <div className="p-4 space-y-3">
            {onlineUsers.map((user, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback
                      className={
                        user.isTeacher
                          ? "bg-brain-100 text-brain-700"
                          : "bg-muted"
                      }
                    >
                      {user.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div
                    className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${
                      user.status === "online"
                        ? "bg-green-500"
                        : user.status === "away"
                          ? "bg-yellow-500"
                          : "bg-gray-400"
                    }`}
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-1">
                    <span className="text-sm font-medium">{user.name}</span>
                    {user.isTeacher && (
                      <Badge variant="secondary" className="text-xs">
                        Prof
                      </Badge>
                    )}
                  </div>
                  <span className="text-xs text-muted-foreground capitalize">
                    {user.status === "online"
                      ? "Online"
                      : user.status === "away"
                        ? "Ausente"
                        : "Offline"}
                  </span>
                </div>
                <Button size="sm" variant="ghost">
                  <MessageCircle className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClassChat;
