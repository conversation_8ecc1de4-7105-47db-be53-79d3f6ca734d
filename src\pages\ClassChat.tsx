import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Brain,
  Send,
  Search,
  Users,
  MessageCircle,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Pin,
  Settings,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import {
  getChatMessages,
  sendChatMessage,
  getAllStudents,
  getCurrentUser,
  subscribeToChatMessages
} from "@/lib/supabase";

const ClassChat = () => {
  const navigate = useNavigate();
  const [message, setMessage] = useState("");
  const [selectedChannel, setSelectedChannel] = useState("geral");
  const [messages, setMessages] = useState([]);
  const [users, setUsers] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    loadData();
    setupRealtimeSubscription();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [user, chatMessages, allUsers] = await Promise.all([
        getCurrentUser(),
        getChatMessages(),
        getAllStudents()
      ]);

      setCurrentUser(user);
      setMessages(chatMessages);
      setUsers(allUsers);
    } catch (error) {
      console.error("Error loading chat data:", error);
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeSubscription = () => {
    const subscription = subscribeToChatMessages((payload) => {
      if (payload.eventType === 'INSERT') {
        setMessages(prev => [...prev, payload.new]);
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  };

  const channels = [
    {
      id: "geral",
      name: "💬 Geral",
      description: "Discussões gerais da turma",
      members: users.length,
      unread: 0,
    },
    {
      id: "duvidas",
      name: "❓ Dúvidas",
      description: "Tire suas dúvidas aqui",
      members: users.length,
      unread: 0,
    },
    {
      id: "avisos",
      name: "📢 Avisos",
      description: "Comunicados importantes",
      members: users.length,
      unread: 0,
    },
  ];

  const sendMessage = async () => {
    if (message.trim() && currentUser) {
      try {
        await sendChatMessage({
          content: message.trim(),
          type: 'text'
        });
        setMessage("");
      } catch (error) {
        console.error("Error sending message:", error);
        alert("Erro ao enviar mensagem");
      }
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const onlineUsers = users.slice(0, 10).map(user => ({
    name: user.name,
    avatar: getInitials(user.name),
    status: "online", // In a real app, this would be tracked
    isTeacher: user.user_type === 'master'
  }));

  const currentChannel = channels.find((ch) => ch.id === selectedChannel);

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header title="StudyHub" />
        <div className="flex items-center justify-center h-[calc(100vh-80px)]">
          <p className="text-muted-foreground">Carregando chat...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header title="StudyHub" />

      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar - Channels */}
        <div className="w-80 border-r bg-card flex flex-col">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold mb-4">Chat da Turma</h2>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Buscar conversas..." className="pl-10" />
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-2">
              {channels.map((channel) => (
                <div
                  key={channel.id}
                  onClick={() => setSelectedChannel(channel.id)}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedChannel === channel.id
                      ? "bg-primary/10 border border-primary/20"
                      : "hover:bg-muted/50"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium text-sm">{channel.name}</h3>
                        {channel.unread > 0 && (
                          <Badge
                            variant="destructive"
                            className="h-5 w-5 p-0 text-xs flex items-center justify-center"
                          >
                            {channel.unread}
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {channel.description}
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Users className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          {channel.members} membros
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Header */}
          <div className="p-4 border-b bg-card">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{currentChannel?.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {currentChannel?.members} membros •{" "}
                  {currentChannel?.description}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="ghost">
                  <Phone className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Video className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Pin className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.length > 0 ? (
              messages.map((msg) => {
                const isMe = currentUser && msg.sender_id === currentUser.id;
                const isTeacher = msg.sender?.user_type === 'master';
                const senderName = msg.sender?.name || 'Usuário';
                const avatar = getInitials(senderName);

                return (
                  <div
                    key={msg.id}
                    className={`flex space-x-3 ${isMe ? "flex-row-reverse space-x-reverse" : ""}`}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback
                        className={
                          isTeacher ? "bg-brain-100 text-brain-700" : "bg-muted"
                        }
                      >
                        {avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div
                      className={`flex-1 max-w-xs md:max-w-md ${isMe ? "text-right" : ""}`}
                    >
                      <div className="flex items-center space-x-2 mb-1">
                        {!isMe && (
                          <>
                            <span
                              className={`text-sm font-medium ${isTeacher ? "text-brain-700" : ""}`}
                            >
                              {senderName}
                              {isTeacher && (
                                <Badge variant="secondary" className="ml-1 text-xs">
                                  Prof
                                </Badge>
                              )}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {formatTime(msg.created_at)}
                            </span>
                          </>
                        )}
                        {isMe && (
                          <>
                            <span className="text-xs text-muted-foreground">
                              {formatTime(msg.created_at)}
                            </span>
                            <span className="text-sm font-medium">Você</span>
                          </>
                        )}
                      </div>
                      <div
                        className={`p-3 rounded-lg text-sm ${
                          isMe
                            ? "bg-primary text-primary-foreground"
                            : isTeacher
                              ? "bg-brain-50 border border-brain-200"
                              : "bg-muted"
                        }`}
                      >
                        {msg.content}
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <MessageCircle className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">
                    Nenhuma mensagem ainda
                  </h3>
                  <p className="text-muted-foreground">
                    Seja o primeiro a enviar uma mensagem!
                  </p>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="p-4 border-t bg-card">
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="ghost">
                <Paperclip className="h-4 w-4" />
              </Button>
              <div className="flex-1 relative">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder={`Enviar mensagem para ${currentChannel?.name}`}
                  onKeyPress={(e) => e.key === "Enter" && sendMessage()}
                  className="pr-10"
                />
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2"
                >
                  <Smile className="h-4 w-4" />
                </Button>
              </div>
              <Button
                onClick={sendMessage}
                disabled={!message.trim()}
                className="bg-gradient-to-r from-brain-500 to-cognitive-500"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Online Users */}
        <div className="w-64 border-l bg-card">
          <div className="p-4 border-b">
            <h3 className="font-semibold">Usuários Online</h3>
            <p className="text-sm text-muted-foreground">
              {onlineUsers.filter((u) => u.status === "online").length} online
            </p>
          </div>
          <div className="p-4 space-y-3">
            {onlineUsers.map((user, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback
                      className={
                        user.isTeacher
                          ? "bg-brain-100 text-brain-700"
                          : "bg-muted"
                      }
                    >
                      {user.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div
                    className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${
                      user.status === "online"
                        ? "bg-green-500"
                        : user.status === "away"
                          ? "bg-yellow-500"
                          : "bg-gray-400"
                    }`}
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-1">
                    <span className="text-sm font-medium">{user.name}</span>
                    {user.isTeacher && (
                      <Badge variant="secondary" className="text-xs">
                        Prof
                      </Badge>
                    )}
                  </div>
                  <span className="text-xs text-muted-foreground capitalize">
                    {user.status === "online"
                      ? "Online"
                      : user.status === "away"
                        ? "Ausente"
                        : "Offline"}
                  </span>
                </div>
                <Button size="sm" variant="ghost">
                  <MessageCircle className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClassChat;
