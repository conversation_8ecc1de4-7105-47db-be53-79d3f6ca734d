// Simple test script to verify database connection and schema
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://azalziifkdybvaxijeqa.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF6YWx6aWlma2R5YnZheGlqZXFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2OTI0NzIsImV4cCI6MjA2NjI2ODQ3Mn0.9JsdWVkw0-7OYpegOHD76xedzbJx2QUEpgULeF2fJ_E';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDatabase() {
  console.log('Testing database connection...');
  
  try {
    // Test 1: Check if profiles table exists
    console.log('\n1. Testing profiles table access...');
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Error accessing profiles table:', error.message);
      if (error.code === '42P01') {
        console.log('📝 The profiles table does not exist. Please run the database-setup.sql script.');
      }
    } else {
      console.log('✅ Profiles table accessible');
      if (data && data.length > 0) {
        console.log('📊 Sample profile columns:', Object.keys(data[0]));
      }
    }
    
    // Test 2: Check actual table structure
    console.log('\n2. Checking actual table structure...');
    try {
      // Try to get the actual table structure by querying with all possible columns
      const { data: structureData, error: structureError } = await supabase
        .from('profiles')
        .select('*')
        .limit(1);

      if (structureData && structureData.length > 0) {
        console.log('📊 Actual columns in profiles table:', Object.keys(structureData[0]));
      } else if (!structureError) {
        console.log('📊 Table exists but is empty');
      }
    } catch (err) {
      console.log('❌ Could not determine table structure');
    }

    // Test 3: Check specific columns individually
    console.log('\n3. Testing required columns individually...');
    const requiredColumns = ['user_type', 'status', 'learning_goals', 'study_preferences'];

    for (const column of requiredColumns) {
      try {
        const { error } = await supabase
          .from('profiles')
          .select(column)
          .limit(1);

        if (error) {
          console.log(`❌ Column '${column}' error:`, error.message);
        } else {
          console.log(`✅ Column '${column}' exists`);
        }
      } catch (columnError) {
        console.log(`❌ Column '${column}' exception:`, columnError.message);
      }
    }
    
    // Test 4: Check auth connection
    console.log('\n4. Testing auth connection...');
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.error('❌ Auth error:', authError.message);
    } else {
      console.log('✅ Auth connection working');
      console.log('🔐 Current session:', authData.session ? 'Active' : 'None');
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

testDatabase();
