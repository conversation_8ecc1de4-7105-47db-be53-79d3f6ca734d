-- SQL para implementar sistema de categorias de curso
-- Execute este script no Supabase SQL Editor

-- 1. Atualizar tabela de conteúdo para incluir categoria do curso
ALTER TABLE content ADD COLUMN IF NOT EXISTS course_category TEXT;

-- 2. Atualizar tabela de chat_messages para incluir categoria do curso
ALTER TABLE chat_messages ADD COLUMN IF NOT EXISTS course_category TEXT;

-- 3. <PERSON><PERSON><PERSON> índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_content_course_category ON content(course_category);
CREATE INDEX IF NOT EXISTS idx_chat_messages_course_category ON chat_messages(course_category);
CREATE INDEX IF NOT EXISTS idx_profiles_course ON profiles(course);

-- 4. Atualizar políticas RLS para content
DROP POLICY IF EXISTS "Users can view published content" ON content;
CREATE POLICY "Users can view published content" ON content
FOR SELECT USING (
  status = 'published' AND
  auth.role() = 'authenticated' AND
  (
    course_category IS NULL OR 
    course_category = '' OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.course = content.course_category
    )
  )
);

-- 5. Atualizar políticas RLS para chat_messages
DROP POLICY IF EXISTS "Users can view chat messages" ON chat_messages;
CREATE POLICY "Users can view chat messages" ON chat_messages
FOR SELECT USING (
  auth.role() = 'authenticated' AND
  (
    course_category IS NULL OR 
    course_category = '' OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.course = chat_messages.course_category
    )
  )
);

DROP POLICY IF EXISTS "Users can send chat messages" ON chat_messages;
CREATE POLICY "Users can send chat messages" ON chat_messages
FOR INSERT WITH CHECK (
  auth.role() = 'authenticated' AND
  sender_id = auth.uid() AND
  (
    course_category IS NULL OR 
    course_category = '' OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.course = course_category
    )
  )
);

-- 6. Política para masters verem todo conteúdo
CREATE POLICY "Masters can view all content" ON content
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.user_type = 'master'
  )
);

CREATE POLICY "Masters can view all chat messages" ON chat_messages
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.user_type = 'master'
  )
);

-- 7. Função para mapear grupos de estudo para categorias
CREATE OR REPLACE FUNCTION map_study_group_to_category(study_group TEXT)
RETURNS TEXT AS $$
BEGIN
  CASE study_group
    WHEN 'TCC - Criança e Adolescente' THEN RETURN 'TCC - Infancia e adolescencia';
    WHEN 'TCC - Adultos' THEN RETURN 'TCC - Adultos';
    WHEN 'Análise do Comportamento' THEN RETURN 'Análise do Comportamento';
    WHEN 'Neuropsicologia' THEN RETURN 'Neuropsicologia';
    ELSE RETURN study_group;
  END CASE;
END;
$$ LANGUAGE plpgsql;
