# Supabase Storage Setup for File Uploads

## 🚨 Required Setup for File Uploads

To enable file uploads in your application, you need to set up Supabase Storage:

### Step 1: Create Storage Bucket

1. Go to your Supabase dashboard: https://supabase.com/dashboard
2. Navigate to your project: **azalziifkdybvaxijeqa**
3. Go to **Storage** in the left sidebar
4. Click **New Bucket**
5. Configure the bucket:
   - **Name**: `content-files`
   - **Public**: ✅ **Enable** (so files can be accessed via URL)
   - **File size limit**: 5GB (or your preferred limit)
   - **Allowed MIME types**: Leave empty for all types

### Step 2: Set Storage Policies

After creating the bucket, you need to set up RLS policies:

1. Go to **Storage** → **Policies**
2. Click **New Policy** for the `content-files` bucket
3. Create these policies:

#### Policy 1: Allow Masters to Upload Files
```sql
-- Policy Name: Masters can upload files
-- Operation: INSERT
-- Target roles: authenticated

-- Policy definition:
(EXISTS (
  SELECT 1 FROM profiles 
  WHERE profiles.id = auth.uid() 
  AND profiles.user_type = 'master'
))
```

#### Policy 2: Allow Everyone to View Files
```sql
-- Policy Name: Anyone can view files
-- Operation: SELECT
-- Target roles: authenticated, anon

-- Policy definition:
true
```

#### Policy 3: Allow Masters to Delete Files
```sql
-- Policy Name: Masters can delete files
-- Operation: DELETE
-- Target roles: authenticated

-- Policy definition:
(EXISTS (
  SELECT 1 FROM profiles 
  WHERE profiles.id = auth.uid() 
  AND profiles.user_type = 'master'
))
```

### Step 3: Test File Upload

1. Go to your application
2. Login as a master user
3. Navigate to the Upload tab
4. Try uploading a file (MP4, PDF, PPTX, DOCX)
5. The file should upload and be saved to Supabase Storage

### Step 4: Verify Storage

After uploading, you can verify in Supabase:

1. Go to **Storage** → **content-files** bucket
2. You should see your uploaded files in the `content/` folder
3. Files will have URLs like: `https://[project-id].supabase.co/storage/v1/object/public/content-files/content/[filename]`

## How It Works

### File Upload Process:
1. User selects a file in the upload form
2. File is uploaded to Supabase Storage (`content-files` bucket)
3. Supabase returns a public URL for the file
4. Content record is created in the database with the file URL
5. Students can access files via the public URL

### File Storage Structure:
```
content-files/
└── content/
    ├── 1703123456789-abc123.pdf
    ├── 1703123456790-def456.mp4
    └── 1703123456791-ghi789.pptx
```

### Supported File Types:
- **Videos**: MP4
- **Documents**: PDF, DOCX
- **Presentations**: PPTX
- **Maximum size**: 5GB per file

## Troubleshooting

### Common Issues:

1. **"Storage bucket not found"**
   - Make sure you created the `content-files` bucket
   - Check the bucket name is exactly `content-files`

2. **"Permission denied"**
   - Verify RLS policies are set up correctly
   - Make sure the user has `user_type = 'master'` in profiles table

3. **"File too large"**
   - Check the file size limit in bucket settings
   - Default limit is 5GB

4. **"Invalid file type"**
   - Make sure the file extension is supported
   - Check the `accept` attribute in the file input

### Testing Storage Policies:

You can test policies in the Supabase dashboard:
1. Go to **Storage** → **Policies**
2. Click **Test Policy** next to each policy
3. Use different user contexts to verify access

## Security Notes

- Files are stored in a **public bucket** - anyone with the URL can access them
- Only master users can upload/delete files
- File URLs are permanent and don't expire
- Consider implementing virus scanning for production use

## Alternative: Private Storage

If you need private file access:

1. Create a **private bucket** instead
2. Generate signed URLs for temporary access
3. Implement additional authentication in your app

This setup provides a robust, scalable file storage solution using Supabase's built-in storage capabilities!
