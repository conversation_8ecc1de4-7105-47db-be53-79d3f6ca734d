import React, { useState, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "./ui/tabs";
import { Badge } from "./ui/badge";
import { Modal } from "./ui/modal";
import {
  DollarSign,
  CreditCard,
  Calendar,
  Plus,
  Edit,
  Trash2,
  Users,
  TrendingUp,
  Clock,
  CheckCircle
} from "lucide-react";
import {
  getCourses,
  createCourse,
  updateCourse,
  deleteCourse,
  getPaymentPlans,
  createPaymentPlan,
  updatePaymentPlan
} from "@/lib/supabase";

interface Course {
  id: string;
  name: string;
  price: number;
  installments: number[];
  description: string;
  duration: string;
  active: boolean;
}

interface PaymentPlan {
  id: string;
  studentId: string;
  studentName: string;
  courseId: string;
  courseName: string;
  totalAmount: number;
  installments: number;
  installmentAmount: number;
  paidInstallments: number;
  status: "active" | "completed" | "overdue" | "cancelled";
  nextDueDate: string;
  paymentMethod: string;
}

interface FinancialManagementProps {
  students: any[];
  onUpdatePayment: (paymentId: string, status: string) => void;
}

export const FinancialManagement: React.FC<FinancialManagementProps> = ({
  students,
  onUpdatePayment,
}) => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [paymentPlans, setPaymentPlans] = useState<PaymentPlan[]>([]);
  const [showCourseModal, setShowCourseModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<any>(null);

  const [courseForm, setCourseForm] = useState({
    name: "",
    price: "",
    installments: "1,3,6,12",
    description: "",
    duration: "",
  });

  const [paymentForm, setPaymentForm] = useState({
    courseId: "",
    installments: 1,
    paymentMethod: "credit_card",
  });

  // Load initial data
  useEffect(() => {
    loadCourses();
    loadPaymentPlans();
  }, []);

  const loadCourses = async () => {
    try {
      const data = await getCourses();
      const formattedCourses = data.map(course => ({
        id: course.id,
        name: course.name,
        price: course.price,
        installments: course.installment_options || [1, 3, 6, 12],
        description: course.description || "",
        duration: course.duration || "",
        active: course.active,
      }));
      setCourses(formattedCourses);
    } catch (error) {
      console.error("Error loading courses:", error);
    }
  };

  const loadPaymentPlans = async () => {
    try {
      const data = await getPaymentPlans();
      const formattedPlans = data.map(plan => ({
        id: plan.id,
        studentId: plan.student_id,
        studentName: plan.student?.name || "Nome não disponível",
        courseId: plan.course_id,
        courseName: plan.course?.name || "Curso não disponível",
        totalAmount: plan.total_amount,
        installments: plan.installments,
        installmentAmount: plan.installment_amount,
        paidInstallments: plan.paid_installments,
        status: plan.status,
        nextDueDate: plan.next_due_date,
        paymentMethod: plan.payment_method,
      }));
      setPaymentPlans(formattedPlans);
    } catch (error) {
      console.error("Error loading payment plans:", error);
    }
  };

  const handleSaveCourse = async () => {
    try {
      const installmentsArray = courseForm.installments
        .split(",")
        .map(i => parseInt(i.trim()))
        .filter(i => !isNaN(i));

      const courseData = {
        name: courseForm.name,
        price: parseFloat(courseForm.price),
        installment_options: installmentsArray,
        description: courseForm.description,
        duration: courseForm.duration,
      };

      if (editingCourse) {
        await updateCourse(editingCourse.id, courseData);
      } else {
        await createCourse(courseData);
      }

      await loadCourses(); // Reload courses
      resetCourseForm();
      setShowCourseModal(false);
    } catch (error) {
      console.error("Error saving course:", error);
      alert("Erro ao salvar curso");
    }
  };

  const resetCourseForm = () => {
    setCourseForm({
      name: "",
      price: "",
      installments: "1,3,6,12",
      description: "",
      duration: "",
    });
    setEditingCourse(null);
  };

  const handleEditCourse = (course: Course) => {
    setEditingCourse(course);
    setCourseForm({
      name: course.name,
      price: course.price.toString(),
      installments: course.installments.join(","),
      description: course.description,
      duration: course.duration,
    });
    setShowCourseModal(true);
  };

  const handleDeleteCourse = async (courseId: string) => {
    if (confirm("Tem certeza que deseja deletar este curso?")) {
      try {
        await deleteCourse(courseId);
        await loadCourses(); // Reload courses
      } catch (error) {
        console.error("Error deleting course:", error);
        alert("Erro ao deletar curso");
      }
    }
  };

  const handleCreatePaymentPlan = async () => {
    if (!selectedStudent || !paymentForm.courseId) return;

    const course = courses.find(c => c.id === paymentForm.courseId);
    if (!course) return;

    try {
      await createPaymentPlan({
        student_id: selectedStudent.id,
        course_id: course.id,
        total_amount: course.price,
        installments: paymentForm.installments,
        payment_method: paymentForm.paymentMethod,
      });

      await loadPaymentPlans(); // Reload payment plans
      setShowPaymentModal(false);
      setSelectedStudent(null);
      setPaymentForm({
        courseId: "",
        installments: 1,
        paymentMethod: "credit_card",
      });
    } catch (error) {
      console.error("Error creating payment plan:", error);
      alert("Erro ao criar plano de pagamento");
    }
  };

  const handlePaymentUpdate = async (planId: string, action: "pay" | "cancel") => {
    try {
      const plan = paymentPlans.find(p => p.id === planId);
      if (!plan) return;

      if (action === "pay") {
        const newPaidInstallments = plan.paidInstallments + 1;
        const isCompleted = newPaidInstallments >= plan.installments;

        await updatePaymentPlan(planId, {
          paid_installments: newPaidInstallments,
          status: isCompleted ? "completed" : "active",
          next_due_date: isCompleted ? null :
            new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        });
      } else {
        await updatePaymentPlan(planId, { status: "cancelled" });
      }

      await loadPaymentPlans(); // Reload payment plans
    } catch (error) {
      console.error("Error updating payment plan:", error);
      alert("Erro ao atualizar plano de pagamento");
    }
  };

  const stats = {
    totalRevenue: paymentPlans.reduce((sum, plan) => 
      sum + (plan.paidInstallments * plan.installmentAmount), 0),
    activeContracts: paymentPlans.filter(p => p.status === "active").length,
    completedContracts: paymentPlans.filter(p => p.status === "completed").length,
    overduePayments: paymentPlans.filter(p => 
      p.status === "active" && new Date(p.nextDueDate) < new Date()).length,
  };

  return (
    <div className="space-y-6">
      {/* Financial Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-2">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <p className="text-2xl font-bold">R$ {stats.totalRevenue.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground">Receita Total</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-2">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <p className="text-2xl font-bold">{stats.activeContracts}</p>
            <p className="text-xs text-muted-foreground">Contratos Ativos</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-2">
              <CheckCircle className="h-6 w-6 text-purple-600" />
            </div>
            <p className="text-2xl font-bold">{stats.completedContracts}</p>
            <p className="text-xs text-muted-foreground">Concluídos</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-2">
              <Clock className="h-6 w-6 text-red-600" />
            </div>
            <p className="text-2xl font-bold">{stats.overduePayments}</p>
            <p className="text-xs text-muted-foreground">Em Atraso</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="courses" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="courses">Cursos e Preços</TabsTrigger>
          <TabsTrigger value="plans">Planos de Pagamento</TabsTrigger>
          <TabsTrigger value="transactions">Transações</TabsTrigger>
        </TabsList>

        {/* Courses Management */}
        <TabsContent value="courses">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Gerenciar Cursos</CardTitle>
                  <CardDescription>
                    Configure cursos, preços e opções de parcelamento
                  </CardDescription>
                </div>
                <Button onClick={() => setShowCourseModal(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Novo Curso
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {courses.map((course) => (
                  <div key={course.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold">{course.name}</h3>
                        <p className="text-sm text-gray-600">{course.description}</p>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <span>💰 R$ {course.price.toLocaleString()}</span>
                          <span>⏱️ {course.duration}</span>
                          <span>📊 {course.installments.join("x, ")}x</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={course.active ? "default" : "secondary"}>
                          {course.active ? "Ativo" : "Inativo"}
                        </Badge>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEditCourse(course)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDeleteCourse(course.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment Plans */}
        <TabsContent value="plans">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Planos de Pagamento</CardTitle>
                  <CardDescription>
                    Gerencie contratos e parcelas dos alunos
                  </CardDescription>
                </div>
                <Button 
                  onClick={() => setShowPaymentModal(true)}
                  disabled={students.length === 0}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Novo Plano
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentPlans.length > 0 ? (
                  paymentPlans.map((plan) => (
                    <div key={plan.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="font-semibold">{plan.studentName}</h3>
                          <p className="text-sm text-gray-600">{plan.courseName}</p>
                          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                            <span>💰 R$ {plan.totalAmount.toLocaleString()}</span>
                            <span>📊 {plan.paidInstallments}/{plan.installments} parcelas</span>
                            <span>📅 Próximo: {plan.nextDueDate || "Concluído"}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            variant={
                              plan.status === "completed" ? "default" :
                              plan.status === "active" ? "secondary" :
                              plan.status === "overdue" ? "destructive" : "outline"
                            }
                          >
                            {plan.status === "completed" ? "Concluído" :
                             plan.status === "active" ? "Ativo" :
                             plan.status === "overdue" ? "Em Atraso" : "Cancelado"}
                          </Badge>
                          {plan.status === "active" && (
                            <>
                              <Button
                                size="sm"
                                onClick={() => handlePaymentUpdate(plan.id, "pay")}
                              >
                                Baixar Parcela
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handlePaymentUpdate(plan.id, "cancel")}
                              >
                                Cancelar
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <CreditCard className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-semibold mb-2">Nenhum plano de pagamento</h3>
                    <p className="text-gray-600">Crie planos de pagamento para seus alunos</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Transactions */}
        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Transações</CardTitle>
              <CardDescription>
                Acompanhe todas as transações financeiras
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">Em desenvolvimento</h3>
                <p className="text-gray-600">Histórico detalhado de transações em breve</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Course Modal */}
      <Modal
        isOpen={showCourseModal}
        onClose={() => {
          setShowCourseModal(false);
          resetCourseForm();
        }}
        title={editingCourse ? "Editar Curso" : "Novo Curso"}
        size="lg"
      >
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="courseName">Nome do Curso</Label>
              <Input
                id="courseName"
                value={courseForm.name}
                onChange={(e) => setCourseForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Ex: Matemática Financeira"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="coursePrice">Preço (R$)</Label>
              <Input
                id="coursePrice"
                type="number"
                value={courseForm.price}
                onChange={(e) => setCourseForm(prev => ({ ...prev, price: e.target.value }))}
                placeholder="497.00"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="courseDescription">Descrição</Label>
            <Input
              id="courseDescription"
              value={courseForm.description}
              onChange={(e) => setCourseForm(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Descrição do curso"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="courseDuration">Duração</Label>
              <Input
                id="courseDuration"
                value={courseForm.duration}
                onChange={(e) => setCourseForm(prev => ({ ...prev, duration: e.target.value }))}
                placeholder="Ex: 40 horas"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="courseInstallments">Opções de Parcelamento</Label>
              <Input
                id="courseInstallments"
                value={courseForm.installments}
                onChange={(e) => setCourseForm(prev => ({ ...prev, installments: e.target.value }))}
                placeholder="1,3,6,12"
              />
              <p className="text-xs text-gray-500">Separar por vírgula (ex: 1,3,6,12)</p>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setShowCourseModal(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSaveCourse}>
              {editingCourse ? "Atualizar" : "Criar"} Curso
            </Button>
          </div>
        </div>
      </Modal>

      {/* Payment Plan Modal */}
      <Modal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        title="Novo Plano de Pagamento"
        size="md"
      >
        <div className="p-6 space-y-4">
          <div className="space-y-2">
            <Label>Selecionar Aluno</Label>
            <select
              value={selectedStudent?.id || ""}
              onChange={(e) => {
                const student = students.find(s => s.id === e.target.value);
                setSelectedStudent(student);
              }}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="">Selecione um aluno...</option>
              {students.map((student) => (
                <option key={student.id} value={student.id}>
                  {student.name} - {student.email}
                </option>
              ))}
            </select>
          </div>
          
          <div className="space-y-2">
            <Label>Selecionar Curso</Label>
            <select
              value={paymentForm.courseId}
              onChange={(e) => setPaymentForm(prev => ({ ...prev, courseId: e.target.value }))}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="">Selecione um curso...</option>
              {courses.filter(c => c.active).map((course) => (
                <option key={course.id} value={course.id}>
                  {course.name} - R$ {course.price.toLocaleString()}
                </option>
              ))}
            </select>
          </div>
          
          <div className="space-y-2">
            <Label>Número de Parcelas</Label>
            <select
              value={paymentForm.installments}
              onChange={(e) => setPaymentForm(prev => ({ ...prev, installments: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border rounded-md"
            >
              {courses.find(c => c.id === paymentForm.courseId)?.installments.map((inst) => (
                <option key={inst} value={inst}>
                  {inst}x de R$ {courses.find(c => c.id === paymentForm.courseId) ? 
                    (courses.find(c => c.id === paymentForm.courseId)!.price / inst).toFixed(2) : "0"}
                </option>
              )) || <option value={1}>Selecione um curso primeiro</option>}
            </select>
          </div>
          
          <div className="space-y-2">
            <Label>Forma de Pagamento</Label>
            <select
              value={paymentForm.paymentMethod}
              onChange={(e) => setPaymentForm(prev => ({ ...prev, paymentMethod: e.target.value }))}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="credit_card">Cartão de Crédito</option>
              <option value="debit_card">Cartão de Débito</option>
              <option value="pix">PIX</option>
              <option value="bank_slip">Boleto</option>
            </select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setShowPaymentModal(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={handleCreatePaymentPlan}
              disabled={!selectedStudent || !paymentForm.courseId}
            >
              Criar Plano
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
