-- Enhanced database schema for real-time synchronization
-- Run this in Supabase SQL Editor

-- 1. Create courses table
CREATE TABLE IF NOT EXISTS courses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  duration TEXT,
  installment_options INTEGER[] DEFAULT '{1,3,6,12}',
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 2. Create payment_plans table
CREATE TABLE IF NOT EXISTS payment_plans (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  total_amount DECIMAL(10,2) NOT NULL,
  installments INTEGER NOT NULL DEFAULT 1,
  installment_amount DECIMAL(10,2) NOT NULL,
  paid_installments INTEGER DEFAULT 0,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'overdue', 'cancelled')),
  payment_method TEXT DEFAULT 'credit_card' CHECK (payment_method IN ('credit_card', 'debit_card', 'pix', 'bank_slip')),
  next_due_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 3. Create content table
CREATE TABLE IF NOT EXISTS content (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('video', 'document', 'audio', 'image')),
  file_url TEXT,
  file_size BIGINT,
  duration INTEGER, -- in seconds for videos/audio
  status TEXT DEFAULT 'processing' CHECK (status IN ('processing', 'published', 'draft', 'archived')),
  views INTEGER DEFAULT 0,
  downloads INTEGER DEFAULT 0,
  course_id UUID REFERENCES courses(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 4. Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  read BOOLEAN DEFAULT false,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create student_progress table
CREATE TABLE IF NOT EXISTS student_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  content_id UUID REFERENCES content(id) ON DELETE CASCADE,
  progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  completed BOOLEAN DEFAULT false,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  time_spent INTEGER DEFAULT 0, -- in seconds
  UNIQUE(student_id, content_id)
);

-- 6. Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  sender_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  type TEXT DEFAULT 'text' CHECK (type IN ('text', 'file', 'image')),
  file_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Add RLS policies

-- Courses policies
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Masters can manage courses" ON courses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

CREATE POLICY "Students can view active courses" ON courses
  FOR SELECT USING (active = true);

-- Payment plans policies
ALTER TABLE payment_plans ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Masters can manage all payment plans" ON payment_plans
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

CREATE POLICY "Students can view their own payment plans" ON payment_plans
  FOR SELECT USING (student_id = auth.uid());

-- Content policies
ALTER TABLE content ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Masters can manage content" ON content
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

CREATE POLICY "Students can view published content" ON content
  FOR SELECT USING (status = 'published');

-- Notifications policies
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Masters can create notifications for any user" ON notifications
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

-- Student progress policies
ALTER TABLE student_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Students can manage their own progress" ON student_progress
  FOR ALL USING (student_id = auth.uid());

CREATE POLICY "Masters can view all student progress" ON student_progress
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

-- Chat messages policies
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view all chat messages" ON chat_messages
  FOR SELECT USING (true);

CREATE POLICY "Users can create chat messages" ON chat_messages
  FOR INSERT WITH CHECK (sender_id = auth.uid());

-- 8. Create functions for real-time updates

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_plans_updated_at BEFORE UPDATE ON payment_plans
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_updated_at BEFORE UPDATE ON content
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to create notification when payment status changes
CREATE OR REPLACE FUNCTION notify_payment_status_change()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.status != NEW.status THEN
    INSERT INTO notifications (user_id, title, message, type)
    VALUES (
      NEW.student_id,
      'Status do Pagamento Atualizado',
      CASE 
        WHEN NEW.status = 'completed' THEN 'Parabéns! Seu pagamento foi concluído com sucesso.'
        WHEN NEW.status = 'overdue' THEN 'Atenção: Seu pagamento está em atraso.'
        WHEN NEW.status = 'cancelled' THEN 'Seu plano de pagamento foi cancelado.'
        ELSE 'Status do seu pagamento foi atualizado para: ' || NEW.status
      END,
      CASE 
        WHEN NEW.status = 'completed' THEN 'success'
        WHEN NEW.status = 'overdue' THEN 'warning'
        WHEN NEW.status = 'cancelled' THEN 'error'
        ELSE 'info'
      END
    );
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER payment_status_notification AFTER UPDATE ON payment_plans
  FOR EACH ROW EXECUTE FUNCTION notify_payment_status_change();

-- Function to create notification when new content is published
CREATE OR REPLACE FUNCTION notify_new_content()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.status != 'published' AND NEW.status = 'published' THEN
    INSERT INTO notifications (user_id, title, message, type)
    SELECT 
      profiles.id,
      'Novo Conteúdo Disponível',
      'Novo conteúdo foi publicado: ' || NEW.title,
      'info'
    FROM profiles 
    WHERE profiles.user_type = 'student' 
    AND profiles.status = 'approved';
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER new_content_notification AFTER UPDATE ON content
  FOR EACH ROW EXECUTE FUNCTION notify_new_content();

-- 9. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payment_plans_student_id ON payment_plans(student_id);
CREATE INDEX IF NOT EXISTS idx_payment_plans_status ON payment_plans(status);
CREATE INDEX IF NOT EXISTS idx_content_status ON content(status);
CREATE INDEX IF NOT EXISTS idx_content_course_id ON content(course_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_student_progress_student_id ON student_progress(student_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);

-- 10. Enable real-time subscriptions
ALTER PUBLICATION supabase_realtime ADD TABLE courses;
ALTER PUBLICATION supabase_realtime ADD TABLE payment_plans;
ALTER PUBLICATION supabase_realtime ADD TABLE content;
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE student_progress;
ALTER PUBLICATION supabase_realtime ADD TABLE chat_messages;
