import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function signIn(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error("Supabase auth error:", error);
      return { data: { user: null, session: null }, error };
    }
    
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error during sign in:", err);
    return { 
      data: { user: null, session: null }, 
      error: { message: "Erro inesperado durante o login. Tente novamente." } 
    };
  }
}

export async function signUp(email: string, password: string, metadata = {}) {
  try {
    // Create the auth user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });

    if (error) throw error;
    
    if (data.user) {
      // Create the user profile
      const userType = metadata.user_type || 'student';
      const status = userType === 'master' ? 'approved' : 'pending';
      
      try {
        await createUserProfile(
          data.user.id,
          email,
          userType as 'student' | 'master' | 'admin',
          status as 'pending' | 'approved' | 'suspended',
          metadata
        );
      } catch (profileError) {
        console.error("Error creating profile, but auth user was created:", profileError);
        // Continue anyway since the auth user was created
      }
    }
    
    return data;
  } catch (error) {
    console.error("Error in signUp:", error);
    throw error;
  }
}

export async function signOut() {
  const { error } = await supabase.auth.signOut();
  return { error };
}

export async function getCurrentUser() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error("Error getting current user:", error);
      return null;
    }
    
    return user;
  } catch (error) {
    console.error("Error in getCurrentUser:", error);
    return null;
  }
}

export interface Profile {
  id: string;
  name?: string | null;
  email: string;
  user_type: 'student' | 'master' | 'admin';
  status: 'pending' | 'approved' | 'suspended';
  created_at: string;
  updated_at?: string;
  approved_by?: string;
  course?: string | null;
  semester?: string | null;
  phone?: string | null;
  learning_goals?: string | null;
  study_preferences?: string | null;
  notifications_enabled?: boolean;
}

export async function getUserProfile(userId: string): Promise<Profile | null> {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching user profile:", error);
      return null;
    }

    if (!data) {
      return null;
    }

    // Ensure required fields have default values if missing
    const profile = {
      ...data,
      user_type: data.user_type || 'student',
      status: data.status || 'pending',
      notifications_enabled: data.notifications_enabled !== undefined ? data.notifications_enabled : true
    };

    // If user_type was missing, try to update it in the database
    if (!data.user_type) {
      try {
        await supabase
          .from("profiles")
          .update({ user_type: 'student' })
          .eq("id", userId);
      } catch (updateError) {
        console.error("Error updating user_type:", updateError);
        // Continue anyway, we have the local value
      }
    }

    return profile as Profile;
  } catch (error) {
    console.error("Error in getUserProfile:", error);
    return null;
  }
}

export async function getPendingStudents() {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("user_type", "student")
    .eq("status", "pending")
    .order("created_at", { ascending: false });
  
  if (error) {
    console.error("Error fetching pending students:", error);
    return [];
  }
  
  return data as Profile[];
}

export async function getAllStudents() {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("user_type", "student")
    .order("created_at", { ascending: false });
  
  if (error) {
    console.error("Error fetching all students:", error);
    return [];
  }
  
  return data as Profile[];
}

export async function updateUserStatus(
  userId: string, 
  status: 'pending' | 'approved' | 'suspended',
  approvedBy?: string
) {
  const updateData: any = {
    status,
    updated_at: new Date().toISOString()
  };
  
  if (approvedBy) {
    updateData.approved_by = approvedBy;
  }
  
  const { error } = await supabase
    .from("profiles")
    .update(updateData)
    .eq("id", userId);
  
  if (error) {
    console.error("Error updating user status:", error);
    throw error;
  }
  
  return true;
}

export async function createUserProfile(
  userId: string,
  email: string,
  userType: 'student' | 'master' | 'admin' = 'student',
  status: 'pending' | 'approved' | 'suspended' = 'pending',
  metadata = {}
) {
  try {
    // Start with minimal required fields that should always exist
    const profileData: any = {
      id: userId,
      email,
      created_at: new Date().toISOString(),
      name: metadata.name || ''
    };

    // Try to add optional fields one by one, catching errors for missing columns
    const fieldsToTry = [
      { key: 'user_type', value: userType },
      { key: 'status', value: status },
      { key: 'course', value: metadata.course || null },
      { key: 'semester', value: metadata.semester || null },
      { key: 'phone', value: metadata.phone || null },
      { key: 'learning_goals', value: metadata.learning_goals || null },
      { key: 'study_preferences', value: metadata.study_preferences || null },
      { key: 'notifications_enabled', value: metadata.notifications_enabled !== undefined ? metadata.notifications_enabled : true }
    ];

    // Test which columns exist by trying a select with each column
    for (const field of fieldsToTry) {
      try {
        await supabase
          .from('profiles')
          .select(field.key)
          .limit(1);

        // If no error, the column exists, so add it to profileData
        profileData[field.key] = field.value;
      } catch (columnError) {
        console.log(`Column ${field.key} doesn't exist, skipping`);
      }
    }

    console.log("Creating profile with data:", profileData);

    const { error } = await supabase
      .from("profiles")
      .insert(profileData);

    if (error) {
      console.error("Error inserting profile:", error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error("Error creating user profile:", error);
    throw error;
  }
}

// Add this function to seed test users
export async function seedTestUsers() {
  try {
    // Check if test users already exist
    const { data: existingMaster } = await supabase
      .from("profiles")
      .select("*")
      .eq("email", "<EMAIL>")
      .single();
    
    const { data: existingStudent } = await supabase
      .from("profiles")
      .select("*")
      .eq("email", "<EMAIL>")
      .single();
    
    // If both test users exist, return
    if (existingMaster && existingStudent) {
      console.log("Test users already exist");
      return;
    }
    
    // Create master user if it doesn't exist
    if (!existingMaster) {
      // First create auth user
      const { data: masterAuth, error: masterAuthError } = await supabase.auth.admin.createUser({
        email: "<EMAIL>",
        password: "master123",
        email_confirm: true
      });
      
      if (masterAuthError) {
        console.error("Error creating master auth user:", masterAuthError);
        return;
      }
      
      // Then create profile
      if (masterAuth.user) {
        const { error: masterProfileError } = await supabase
          .from("profiles")
          .insert({
            id: masterAuth.user.id,
            email: "<EMAIL>",
            name: "Admin Master",
            user_type: "master",
            status: "approved",
            created_at: new Date().toISOString()
          });
        
        if (masterProfileError) {
          console.error("Error creating master profile:", masterProfileError);
        }
      }
    }
    
    // Create student user if it doesn't exist
    if (!existingStudent) {
      // First create auth user
      const { data: studentAuth, error: studentAuthError } = await supabase.auth.admin.createUser({
        email: "<EMAIL>",
        password: "aluno123",
        email_confirm: true
      });
      
      if (studentAuthError) {
        console.error("Error creating student auth user:", studentAuthError);
        return;
      }
      
      // Then create profile
      if (studentAuth.user) {
        const { error: studentProfileError } = await supabase
          .from("profiles")
          .insert({
            id: studentAuth.user.id,
            email: "<EMAIL>",
            name: "Aluno Teste",
            user_type: "student",
            status: "approved",
            course: "Engenharia",
            semester: "3º",
            created_at: new Date().toISOString()
          });
        
        if (studentProfileError) {
          console.error("Error creating student profile:", studentProfileError);
        }
      }
    }
    
    console.log("Test users created successfully");
  } catch (err) {
    console.error("Error seeding test users:", err);
  }
}

export async function initializeDatabase() {
  try {
    // Check if profiles table exists by trying to query it
    const { error } = await supabase
      .from("profiles")
      .select("id")
      .limit(1);

    if (error && error.code === "42P01") { // Table doesn't exist
      console.log("Profiles table doesn't exist. Please create it manually in Supabase.");
      console.log("Required SQL:");
      console.log(`
        CREATE TABLE profiles (
          id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
          email TEXT NOT NULL,
          name TEXT,
          user_type TEXT CHECK (user_type IN ('student', 'master', 'admin')) DEFAULT 'student',
          status TEXT CHECK (status IN ('pending', 'approved', 'suspended')) DEFAULT 'pending',
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ,
          approved_by UUID REFERENCES auth.users(id),
          course TEXT,
          semester TEXT,
          phone TEXT,
          learning_goals TEXT,
          study_preferences TEXT,
          notifications_enabled BOOLEAN DEFAULT true
        );

        -- Enable RLS
        ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
        CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
        CREATE POLICY "Masters can view all profiles" ON profiles FOR SELECT USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type IN ('master', 'admin')
          )
        );
        CREATE POLICY "Masters can update student profiles" ON profiles FOR UPDATE USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type IN ('master', 'admin')
          )
        );
      `);
      return false;
    }

    if (error) {
      console.error("Error checking profiles table:", error);
      return false;
    }

    console.log("Database initialization completed successfully");
    return true;
  } catch (error) {
    console.error("Error initializing database:", error);
    return false;
  }
}

export async function fixUserProfiles() {
  try {
    // First check if the user_type column exists by trying to select it
    const { data: testData, error: testError } = await supabase
      .from("profiles")
      .select("user_type")
      .limit(1);

    if (testError && testError.code === "42703") {
      console.log("user_type column doesn't exist in profiles table. Skipping profile fixes.");
      return;
    }

    // Get all profiles without user_type
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .is("user_type", null);

    if (error) {
      console.error("Error fetching profiles without user_type:", error);
      return;
    }

    // Update each profile to set user_type to student
    for (const profile of data || []) {
      try {
        await supabase
          .from("profiles")
          .update({ user_type: 'student' })
          .eq("id", profile.id);
      } catch (updateError) {
        console.error(`Error updating profile ${profile.id}:`, updateError);
      }
    }

    console.log(`Fixed ${data?.length || 0} profiles without user_type`);
  } catch (error) {
    console.error("Error fixing user profiles:", error);
  }
}

// Call this function when the app starts
export async function setupApp() {
  try {
    console.log("Setting up application...");

    const dbInitialized = await initializeDatabase();
    if (!dbInitialized) {
      console.warn("Database initialization failed. Please check the DATABASE_SETUP.md file for instructions.");
      return false;
    }

    await fixUserProfiles();
    console.log("Application setup completed successfully");
    return true;
  } catch (error) {
    console.error("Error during application setup:", error);
    console.warn("Please check the DATABASE_SETUP.md file for setup instructions.");
    return false;
  }
}







