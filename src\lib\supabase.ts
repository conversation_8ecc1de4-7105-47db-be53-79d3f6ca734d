import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function signIn(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error("Supabase auth error:", error);
      return { data: { user: null, session: null }, error };
    }
    
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error during sign in:", err);
    return { 
      data: { user: null, session: null }, 
      error: { message: "Erro inesperado durante o login. Tente novamente." } 
    };
  }
}

export async function signUp(email: string, password: string, metadata = {}) {
  try {
    // Create the auth user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });

    if (error) throw error;

    if (data.user) {
      // Wait a moment for the trigger to create the profile
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if profile was created by trigger
      const existingProfile = await getUserProfile(data.user.id);

      if (!existingProfile) {
        // Only create profile manually if trigger didn't create it
        const userType = metadata.user_type || 'student';
        const status = userType === 'master' ? 'approved' : 'pending';

        try {
          await createUserProfile(
            data.user.id,
            email,
            userType as 'student' | 'master' | 'admin',
            status as 'pending' | 'approved' | 'suspended',
            metadata
          );
        } catch (profileError) {
          console.error("Error creating profile manually:", profileError);
          // Continue anyway since the auth user was created
        }
      } else {
        // Profile exists, just update it with additional metadata if needed
        try {
          const updateData: any = {};

          // Add any additional fields from metadata
          if (metadata.course) updateData.course = metadata.course;
          if (metadata.semester) updateData.semester = metadata.semester;
          if (metadata.phone) updateData.phone = metadata.phone;
          if (metadata.learning_goals) updateData.learning_goals = metadata.learning_goals;
          if (metadata.study_preferences) updateData.study_preferences = metadata.study_preferences;
          if (metadata.notifications_enabled !== undefined) updateData.notifications_enabled = metadata.notifications_enabled;

          if (Object.keys(updateData).length > 0) {
            updateData.updated_at = new Date().toISOString();

            await supabase
              .from("profiles")
              .update(updateData)
              .eq("id", data.user.id);
          }
        } catch (updateError) {
          console.error("Error updating profile with metadata:", updateError);
        }
      }
    }

    return data;
  } catch (error) {
    console.error("Error in signUp:", error);
    throw error;
  }
}

export async function signOut() {
  const { error } = await supabase.auth.signOut();
  return { error };
}

export async function getCurrentUser() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error("Error getting current user:", error);
      return null;
    }
    
    return user;
  } catch (error) {
    console.error("Error in getCurrentUser:", error);
    return null;
  }
}

export interface Profile {
  id: string;
  name?: string | null;
  email: string;
  user_type: 'student' | 'master' | 'admin';
  status: 'pending' | 'approved' | 'suspended';
  created_at: string;
  updated_at?: string;
  approved_by?: string;
  course?: string | null;
  semester?: string | null;
  phone?: string | null;
  learning_goals?: string | null;
  study_preferences?: string | null;
  notifications_enabled?: boolean;
}

export async function getUserProfile(userId: string): Promise<Profile | null> {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching user profile:", error);
      return null;
    }

    if (!data) {
      return null;
    }

    // Ensure required fields have default values if missing
    const profile = {
      ...data,
      user_type: data.user_type || 'student',
      status: data.status || 'pending',
      notifications_enabled: data.notifications_enabled !== undefined ? data.notifications_enabled : true
    };

    // If user_type was missing, try to update it in the database
    if (!data.user_type) {
      try {
        await supabase
          .from("profiles")
          .update({ user_type: 'student' })
          .eq("id", userId);
      } catch (updateError) {
        console.error("Error updating user_type:", updateError);
        // Continue anyway, we have the local value
      }
    }

    return profile as Profile;
  } catch (error) {
    console.error("Error in getUserProfile:", error);
    return null;
  }
}

export async function getPendingStudents() {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("user_type", "student")
    .eq("status", "pending")
    .order("created_at", { ascending: false });
  
  if (error) {
    console.error("Error fetching pending students:", error);
    return [];
  }
  
  return data as Profile[];
}

export async function getAllStudents() {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("user_type", "student")
    .order("created_at", { ascending: false });
  
  if (error) {
    console.error("Error fetching all students:", error);
    return [];
  }
  
  return data as Profile[];
}

export async function updateUserStatus(
  userId: string,
  status: 'pending' | 'approved' | 'suspended',
  approvedBy?: string
) {
  const updateData: any = {
    status,
    updated_at: new Date().toISOString()
  };

  if (approvedBy) {
    updateData.approved_by = approvedBy;
  }

  const { error } = await supabase
    .from("profiles")
    .update(updateData)
    .eq("id", userId);

  if (error) {
    console.error("Error updating user status:", error);
    throw error;
  }

  return true;
}

export async function updateUserProfile(
  userId: string,
  updates: {
    name?: string;
    phone?: string;
    course?: string;
    semester?: string;
    learning_goals?: string;
    study_preferences?: string;
    notifications_enabled?: boolean;
  }
) {
  const updateData = {
    ...updates,
    updated_at: new Date().toISOString()
  };

  const { error } = await supabase
    .from("profiles")
    .update(updateData)
    .eq("id", userId);

  if (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }

  return true;
}

// Course management functions
export async function getCourses() {
  try {
    const { data, error } = await supabase
      .from("courses")
      .select("*")
      .eq("active", true)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching courses:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching courses:", error);
    return [];
  }
}

export async function createCourse(courseData: {
  name: string;
  description: string;
  price: number;
  duration: string;
  installment_options: number[];
}) {
  const { data, error } = await supabase
    .from("courses")
    .insert({
      ...courseData,
      created_by: (await getCurrentUser())?.id
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating course:", error);
    throw error;
  }

  return data;
}

export async function updateCourse(courseId: string, updates: any) {
  const { error } = await supabase
    .from("courses")
    .update(updates)
    .eq("id", courseId);

  if (error) {
    console.error("Error updating course:", error);
    throw error;
  }

  return true;
}

export async function deleteCourse(courseId: string) {
  const { error } = await supabase
    .from("courses")
    .update({ active: false })
    .eq("id", courseId);

  if (error) {
    console.error("Error deleting course:", error);
    throw error;
  }

  return true;
}

// Payment plan management functions
export async function getPaymentPlans() {
  try {
    const { data, error } = await supabase
      .from("payment_plans")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching payment plans:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching payment plans:", error);
    return [];
  }
}

export async function createPaymentPlan(planData: {
  student_id: string;
  course_id: string;
  total_amount: number;
  installments: number;
  payment_method: string;
}) {
  const installment_amount = planData.total_amount / planData.installments;
  const next_due_date = new Date();
  next_due_date.setMonth(next_due_date.getMonth() + 1);

  const { data, error } = await supabase
    .from("payment_plans")
    .insert({
      ...planData,
      installment_amount,
      next_due_date: next_due_date.toISOString().split('T')[0],
      created_by: (await getCurrentUser())?.id
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating payment plan:", error);
    throw error;
  }

  return data;
}

export async function updatePaymentPlan(planId: string, updates: any) {
  const { error } = await supabase
    .from("payment_plans")
    .update(updates)
    .eq("id", planId);

  if (error) {
    console.error("Error updating payment plan:", error);
    throw error;
  }

  return true;
}

// Content management functions
export async function getContent() {
  try {
    const { data, error } = await supabase
      .from("content")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching content:", error);
      // Return empty array if table doesn't exist
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching content:", error);
    return [];
  }
}

export async function getPublishedContent() {
  try {
    const { data, error } = await supabase
      .from("content")
      .select("*")
      .eq("status", "published")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching published content:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching published content:", error);
    return [];
  }
}

export async function createContent(contentData: {
  title: string;
  description?: string;
  type: string;
  file_url?: string;
  file_size?: number;
  duration?: number;
  course_id?: string;
}) {
  const { data, error } = await supabase
    .from("content")
    .insert({
      ...contentData,
      created_by: (await getCurrentUser())?.id
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating content:", error);
    throw error;
  }

  return data;
}

export async function updateContent(contentId: string, updates: any) {
  const { error } = await supabase
    .from("content")
    .update(updates)
    .eq("id", contentId);

  if (error) {
    console.error("Error updating content:", error);
    throw error;
  }

  return true;
}

export async function deleteContent(contentId: string) {
  const { error } = await supabase
    .from("content")
    .delete()
    .eq("id", contentId);

  if (error) {
    console.error("Error deleting content:", error);
    throw error;
  }

  return true;
}

// Notification functions
export async function getNotifications(userId: string) {
  const { data, error } = await supabase
    .from("notifications")
    .select("*")
    .eq("user_id", userId)
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching notifications:", error);
    throw error;
  }

  return data || [];
}

export async function markNotificationAsRead(notificationId: string) {
  const { error } = await supabase
    .from("notifications")
    .update({ read: true })
    .eq("id", notificationId);

  if (error) {
    console.error("Error marking notification as read:", error);
    throw error;
  }

  return true;
}

export async function createNotification(notificationData: {
  user_id: string;
  title: string;
  message: string;
  type?: string;
  action_url?: string;
}) {
  const { data, error } = await supabase
    .from("notifications")
    .insert(notificationData)
    .select()
    .single();

  if (error) {
    console.error("Error creating notification:", error);
    throw error;
  }

  return data;
}

// Student progress functions
export async function getStudentProgress(studentId: string) {
  const { data, error } = await supabase
    .from("student_progress")
    .select(`
      *,
      content:content(title, type, course:courses(name))
    `)
    .eq("student_id", studentId)
    .order("last_accessed", { ascending: false });

  if (error) {
    console.error("Error fetching student progress:", error);
    throw error;
  }

  return data || [];
}

export async function updateStudentProgress(
  studentId: string,
  contentId: string,
  progressData: {
    progress_percentage?: number;
    completed?: boolean;
    time_spent?: number;
  }
) {
  const { data, error } = await supabase
    .from("student_progress")
    .upsert({
      student_id: studentId,
      content_id: contentId,
      ...progressData,
      last_accessed: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    console.error("Error updating student progress:", error);
    throw error;
  }

  return data;
}

// Chat functions
export async function getChatMessages() {
  try {
    const { data, error } = await supabase
      .from("chat_messages")
      .select("*")
      .order("created_at", { ascending: true });

    if (error) {
      console.error("Error fetching chat messages:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching chat messages:", error);
    return [];
  }
}

export async function sendChatMessage(messageData: {
  content: string;
  type?: string;
  file_url?: string;
}) {
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    throw new Error("User not authenticated");
  }

  const { data, error } = await supabase
    .from("chat_messages")
    .insert({
      ...messageData,
      sender_id: currentUser.id
    })
    .select(`
      *,
      sender:profiles!chat_messages_sender_id_fkey(name, user_type)
    `)
    .single();

  if (error) {
    console.error("Error sending chat message:", error);
    throw error;
  }

  return data;
}

// Real-time subscription helpers
export function subscribeToTable(
  table: string,
  callback: (payload: any) => void,
  filter?: string
) {
  const subscription = supabase
    .channel(`${table}_changes`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: table,
        filter: filter
      },
      callback
    )
    .subscribe();

  return subscription;
}

export function subscribeToUserNotifications(
  userId: string,
  callback: (payload: any) => void
) {
  return subscribeToTable(
    'notifications',
    callback,
    `user_id=eq.${userId}`
  );
}

export function subscribeToPaymentPlans(callback: (payload: any) => void) {
  return subscribeToTable('payment_plans', callback);
}

export function subscribeToContent(callback: (payload: any) => void) {
  return subscribeToTable('content', callback);
}

export function subscribeToChatMessages(callback: (payload: any) => void) {
  return subscribeToTable('chat_messages', callback);
}

export async function createUserProfile(
  userId: string,
  email: string,
  userType: 'student' | 'master' | 'admin' = 'student',
  status: 'pending' | 'approved' | 'suspended' = 'pending',
  metadata = {}
) {
  try {
    // Check if profile already exists
    const { data: existingProfile } = await supabase
      .from("profiles")
      .select("id")
      .eq("id", userId)
      .single();

    if (existingProfile) {
      console.log("Profile already exists, skipping creation");
      return existingProfile;
    }

    // Start with minimal required fields that should always exist
    const profileData: any = {
      id: userId,
      email,
      created_at: new Date().toISOString(),
      name: metadata.name || ''
    };

    // Try to add optional fields one by one, catching errors for missing columns
    const fieldsToTry = [
      { key: 'user_type', value: userType },
      { key: 'status', value: status },
      { key: 'course', value: metadata.course || null },
      { key: 'semester', value: metadata.semester || null },
      { key: 'phone', value: metadata.phone || null },
      { key: 'learning_goals', value: metadata.learning_goals || null },
      { key: 'study_preferences', value: metadata.study_preferences || null },
      { key: 'notifications_enabled', value: metadata.notifications_enabled !== undefined ? metadata.notifications_enabled : true }
    ];

    // Test which columns exist by trying a select with each column
    for (const field of fieldsToTry) {
      try {
        await supabase
          .from('profiles')
          .select(field.key)
          .limit(1);

        // If no error, the column exists, so add it to profileData
        profileData[field.key] = field.value;
      } catch (columnError) {
        console.log(`Column ${field.key} doesn't exist, skipping`);
      }
    }

    console.log("Creating profile with data:", profileData);

    const { data, error } = await supabase
      .from("profiles")
      .insert(profileData)
      .select()
      .single();

    if (error) {
      console.error("Error inserting profile:", error);

      // If it's a duplicate key error, try to fetch the existing profile
      if (error.code === '23505') {
        console.log("Profile already exists (duplicate key), fetching existing profile");
        const { data: existingData } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", userId)
          .single();

        if (existingData) {
          return existingData;
        }
      }

      // Provide helpful error messages for common schema issues
      if (error.code === "PGRST204" && error.message.includes("schema cache")) {
        const missingColumn = error.message.match(/'([^']+)' column/)?.[1];
        throw new Error(`Database schema error: Missing '${missingColumn}' column. Please check URGENT_FIX_INSTRUCTIONS.md to fix the database schema.`);
      }

      throw error;
    }

    console.log("Profile created successfully:", data);
    return data;
  } catch (error) {
    console.error("Error creating user profile:", error);
    throw error;
  }
}



export async function initializeDatabase() {
  try {
    // Check if profiles table exists by trying to query it
    const { error } = await supabase
      .from("profiles")
      .select("id")
      .limit(1);

    if (error && error.code === "42P01") { // Table doesn't exist
      console.error("❌ Profiles table doesn't exist!");
      console.error("📋 Please check URGENT_FIX_INSTRUCTIONS.md for setup steps");
      return false;
    }

    if (error) {
      console.error("Error checking profiles table:", error);
      return false;
    }

    // Check if critical columns exist
    const criticalColumns = ['user_type', 'status'];
    const missingColumns = [];

    for (const column of criticalColumns) {
      try {
        const { error: columnError } = await supabase
          .from("profiles")
          .select(column)
          .limit(1);

        if (columnError && columnError.code === "42703") {
          missingColumns.push(column);
        }
      } catch (err) {
        missingColumns.push(column);
      }
    }

    if (missingColumns.length > 0) {
      console.error(`❌ Missing critical columns: ${missingColumns.join(', ')}`);
      console.error("📋 Please check URGENT_FIX_INSTRUCTIONS.md to add missing columns");
      return false;
    }

    console.log("✅ Database initialization completed successfully");
    return true;
  } catch (error) {
    console.error("Error initializing database:", error);
    return false;
  }
}

export async function fixUserProfiles() {
  try {
    // First check if the user_type column exists by trying to select it
    const { data: testData, error: testError } = await supabase
      .from("profiles")
      .select("user_type")
      .limit(1);

    if (testError && testError.code === "42703") {
      console.log("user_type column doesn't exist in profiles table. Skipping profile fixes.");
      return;
    }

    // Get all profiles without user_type
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .is("user_type", null);

    if (error) {
      console.error("Error fetching profiles without user_type:", error);
      return;
    }

    // Update each profile to set user_type to student
    for (const profile of data || []) {
      try {
        await supabase
          .from("profiles")
          .update({ user_type: 'student' })
          .eq("id", profile.id);
      } catch (updateError) {
        console.error(`Error updating profile ${profile.id}:`, updateError);
      }
    }

    console.log(`Fixed ${data?.length || 0} profiles without user_type`);
  } catch (error) {
    console.error("Error fixing user profiles:", error);
  }
}

// Call this function when the app starts
export async function setupApp() {
  try {
    console.log("Setting up application...");

    const dbInitialized = await initializeDatabase();
    if (!dbInitialized) {
      console.warn("Database initialization failed. Please check the DATABASE_SETUP.md file for instructions.");
      return false;
    }

    await fixUserProfiles();
    console.log("Application setup completed successfully");
    return true;
  } catch (error) {
    console.error("Error during application setup:", error);
    console.warn("Please check the DATABASE_SETUP.md file for setup instructions.");
    return false;
  }
}







