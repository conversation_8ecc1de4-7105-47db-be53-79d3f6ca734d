import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function signIn(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error("Supabase auth error:", error);
      return { data: { user: null, session: null }, error };
    }
    
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error during sign in:", err);
    return { 
      data: { user: null, session: null }, 
      error: { message: "Erro inesperado durante o login. Tente novamente." } 
    };
  }
}

export async function signUp(email: string, password: string, metadata = {}) {
  try {
    // Create the auth user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });

    if (error) throw error;
    
    if (data.user) {
      // Create the user profile
      const userType = metadata.user_type || 'student';
      const status = userType === 'master' ? 'approved' : 'pending';
      
      try {
        await createUserProfile(
          data.user.id,
          email,
          userType as 'student' | 'master' | 'admin',
          status as 'pending' | 'approved' | 'suspended',
          metadata
        );
      } catch (profileError) {
        console.error("Error creating profile, but auth user was created:", profileError);
        // Continue anyway since the auth user was created
      }
    }
    
    return data;
  } catch (error) {
    console.error("Error in signUp:", error);
    throw error;
  }
}

export async function signOut() {
  const { error } = await supabase.auth.signOut();
  return { error };
}

export async function getCurrentUser() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error("Error getting current user:", error);
      return null;
    }
    
    return user;
  } catch (error) {
    console.error("Error in getCurrentUser:", error);
    return null;
  }
}

export interface Profile {
  id: string;
  name?: string | null;
  email: string;
  user_type: 'student' | 'master' | 'admin';
  status: 'pending' | 'approved' | 'suspended';
  created_at: string;
  updated_at?: string;
  approved_by?: string;
  course?: string | null;
  semester?: string | null;
  phone?: string | null;
  learning_goals?: string | null;
  study_preferences?: string | null;
  notifications_enabled?: boolean;
}

export async function getUserProfile(userId: string): Promise<Profile | null> {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();
    
    if (error) {
      console.error("Error fetching user profile:", error);
      return null;
    }
    
    // Ensure user_type is set
    if (data && !data.user_type) {
      // Set user_type locally but don't try to update the database here
      // This avoids the 400 Bad Request error
      data.user_type = 'student';
      
      // Update the profile in the database with proper format
      try {
        await supabase
          .from("profiles")
          .update({ user_type: 'student' })
          .match({ id: userId });
      } catch (updateError) {
        console.error("Error updating user_type:", updateError);
      }
    }
    
    return data as Profile;
  } catch (error) {
    console.error("Error in getUserProfile:", error);
    return null;
  }
}

export async function getPendingStudents() {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("user_type", "student")
    .eq("status", "pending")
    .order("created_at", { ascending: false });
  
  if (error) {
    console.error("Error fetching pending students:", error);
    return [];
  }
  
  return data as Profile[];
}

export async function getAllStudents() {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("user_type", "student")
    .order("created_at", { ascending: false });
  
  if (error) {
    console.error("Error fetching all students:", error);
    return [];
  }
  
  return data as Profile[];
}

export async function updateUserStatus(
  userId: string, 
  status: 'pending' | 'approved' | 'suspended',
  approvedBy?: string
) {
  const updateData: any = {
    status,
    updated_at: new Date().toISOString()
  };
  
  if (approvedBy) {
    updateData.approved_by = approvedBy;
  }
  
  const { error } = await supabase
    .from("profiles")
    .update(updateData)
    .eq("id", userId);
  
  if (error) {
    console.error("Error updating user status:", error);
    throw error;
  }
  
  return true;
}

export async function createUserProfile(
  userId: string,
  email: string,
  userType: 'student' | 'master' | 'admin' = 'student',
  status: 'pending' | 'approved' | 'suspended' = 'pending',
  metadata = {}
) {
  try {
    // First, get the table structure to see what columns exist
    const { data: columns, error: columnsError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (columnsError) {
      console.error("Error fetching profile columns:", columnsError);
      // If we can't get the columns, use a minimal profile
      const minimalProfile = {
        id: userId,
        email,
        user_type: userType,
        status,
        created_at: new Date().toISOString(),
        name: metadata.name || ''
      };
      
      const { error } = await supabase
        .from("profiles")
        .insert(minimalProfile);
      
      if (error) throw error;
      return true;
    }
    
    // Create profile object with required fields
    const profileData: any = {
      id: userId,
      email,
      user_type: userType,
      status,
      created_at: new Date().toISOString(),
      name: metadata.name || ''
    };
    
    // Only add fields that exist in the table
    const sampleProfile = columns[0] || {};
    
    if ('course' in sampleProfile) profileData.course = metadata.course || null;
    if ('semester' in sampleProfile) profileData.semester = metadata.semester || null;
    if ('phone' in sampleProfile) profileData.phone = metadata.phone || null;
    if ('learning_goals' in sampleProfile) profileData.learning_goals = metadata.learning_goals || null;
    if ('study_preferences' in sampleProfile) profileData.study_preferences = metadata.study_preferences || null;
    if ('notifications_enabled' in sampleProfile) profileData.notifications_enabled = metadata.notifications_enabled !== undefined ? metadata.notifications_enabled : true;
    
    const { error } = await supabase
      .from("profiles")
      .insert(profileData);
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error("Error creating user profile:", error);
    throw error;
  }
}

// Add this function to seed test users
export async function seedTestUsers() {
  try {
    // Check if test users already exist
    const { data: existingMaster } = await supabase
      .from("profiles")
      .select("*")
      .eq("email", "<EMAIL>")
      .single();
    
    const { data: existingStudent } = await supabase
      .from("profiles")
      .select("*")
      .eq("email", "<EMAIL>")
      .single();
    
    // If both test users exist, return
    if (existingMaster && existingStudent) {
      console.log("Test users already exist");
      return;
    }
    
    // Create master user if it doesn't exist
    if (!existingMaster) {
      // First create auth user
      const { data: masterAuth, error: masterAuthError } = await supabase.auth.admin.createUser({
        email: "<EMAIL>",
        password: "master123",
        email_confirm: true
      });
      
      if (masterAuthError) {
        console.error("Error creating master auth user:", masterAuthError);
        return;
      }
      
      // Then create profile
      if (masterAuth.user) {
        const { error: masterProfileError } = await supabase
          .from("profiles")
          .insert({
            id: masterAuth.user.id,
            email: "<EMAIL>",
            name: "Admin Master",
            user_type: "master",
            status: "approved",
            created_at: new Date().toISOString()
          });
        
        if (masterProfileError) {
          console.error("Error creating master profile:", masterProfileError);
        }
      }
    }
    
    // Create student user if it doesn't exist
    if (!existingStudent) {
      // First create auth user
      const { data: studentAuth, error: studentAuthError } = await supabase.auth.admin.createUser({
        email: "<EMAIL>",
        password: "aluno123",
        email_confirm: true
      });
      
      if (studentAuthError) {
        console.error("Error creating student auth user:", studentAuthError);
        return;
      }
      
      // Then create profile
      if (studentAuth.user) {
        const { error: studentProfileError } = await supabase
          .from("profiles")
          .insert({
            id: studentAuth.user.id,
            email: "<EMAIL>",
            name: "Aluno Teste",
            user_type: "student",
            status: "approved",
            course: "Engenharia",
            semester: "3º",
            created_at: new Date().toISOString()
          });
        
        if (studentProfileError) {
          console.error("Error creating student profile:", studentProfileError);
        }
      }
    }
    
    console.log("Test users created successfully");
  } catch (err) {
    console.error("Error seeding test users:", err);
  }
}

export async function initializeDatabase() {
  try {
    // Check if profiles table exists
    const { error } = await supabase
      .from("profiles")
      .select("id")
      .limit(1);
    
    if (error && error.code === "42P01") { // Table doesn't exist
      console.log("Profiles table doesn't exist, creating it...");
      // Create profiles table
      // Note: This would normally be done with migrations in a production app
      // This is just for demonstration purposes
      await supabase.rpc("create_profiles_table");
    }
    
    // Check if learning_goals column exists
    const { data: columns } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    const sampleProfile = columns?.[0] || {};
    
    // If learning_goals column doesn't exist, add it
    if (!('learning_goals' in sampleProfile)) {
      console.log("Adding missing columns to profiles table...");
      try {
        // This would normally be done with migrations
        // For now, we'll just handle the error gracefully
        await supabase.rpc("add_missing_columns_to_profiles");
      } catch (alterError) {
        console.error("Error adding columns to profiles table:", alterError);
        // Continue anyway, we'll handle missing columns in the code
      }
    }
    
    return true;
  } catch (error) {
    console.error("Error initializing database:", error);
    return false;
  }
}

export async function fixUserProfiles() {
  try {
    // Get all profiles without user_type
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .is("user_type", null);
    
    if (error) {
      console.error("Error fetching profiles without user_type:", error);
      return;
    }
    
    // Update each profile to set user_type to student
    for (const profile of data || []) {
      try {
        await supabase
          .from("profiles")
          .update({ user_type: 'student' })
          .match({ id: profile.id });
      } catch (updateError) {
        console.error(`Error updating profile ${profile.id}:`, updateError);
      }
    }
  } catch (error) {
    console.error("Error fixing user profiles:", error);
  }
}

// Call this function when the app starts
export async function setupApp() {
  await initializeDatabase();
  await fixUserProfiles();
}







