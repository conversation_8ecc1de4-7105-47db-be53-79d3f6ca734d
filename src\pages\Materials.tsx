import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Search,
  Download,
  Eye,
  FileText,
  Presentation,
  BookOpen,
  Star,
  Calendar,
  FolderOpen,
  Filter,
  ChevronRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import { SecureFileViewer } from "@/components/SecureFileViewer";
import {
  getPublishedContent,
  getCurrentUser,
  updateContent
} from "@/lib/supabase";

const Materials = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [materials, setMaterials] = useState([]);
  const [loading, setLoading] = useState(true);
  const [favorites, setFavorites] = useState(new Set());
  const [viewerModal, setViewerModal] = useState({
    isOpen: false,
    filePath: "",
    fileName: "",
    fileType: "",
    title: ""
  });

  useEffect(() => {
    loadMaterials();
  }, []);

  const loadMaterials = async () => {
    try {
      setLoading(true);
      const contentData = await getPublishedContent();

      // Filter only document/material content (not videos)
      const materialContent = contentData.filter(content =>
        content.type === 'document' || content.type === 'image' || content.type === 'audio'
      );

      setMaterials(materialContent);
    } catch (error) {
      console.error("Error loading materials:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewMaterial = async (material) => {
    try {
      // Update view count
      await updateContent(material.id, {
        views: (material.views || 0) + 1
      });

      // Open secure viewer instead of download
      if (material.file_url) {
        setViewerModal({
          isOpen: true,
          filePath: material.file_url, // This is actually the file path
          fileName: material.title,
          fileType: material.type,
          title: material.title
        });
      } else {
        alert("Arquivo não disponível para visualização");
      }

      // Reload materials to update view count
      loadMaterials();
    } catch (error) {
      console.error("Error viewing material:", error);
    }
  };

  const toggleFavorite = (materialId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(materialId)) {
      newFavorites.delete(materialId);
    } else {
      newFavorites.add(materialId);
    }
    setFavorites(newFavorites);
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return "N/A";
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case "document":
        return <FileText className="h-4 w-4" />;
      case "image":
        return <Presentation className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeName = (type) => {
    switch (type) {
      case "document":
        return "Documento";
      case "image":
        return "Imagem";
      case "audio":
        return "Áudio";
      default:
        return "Material";
    }
  };

  // Group materials by course
  const folders = materials.reduce((acc, material) => {
    const courseName = material.course?.name || 'Sem categoria';
    const existing = acc.find(folder => folder.name === courseName);
    if (existing) {
      existing.itemCount++;
      existing.materials.push(material);
    } else {
      acc.push({
        id: courseName.toLowerCase().replace(/\s+/g, '-'),
        name: courseName,
        itemCount: 1,
        materials: [material],
        lastUpdated: new Date(material.created_at).toLocaleDateString('pt-BR'),
        color: `bg-${['blue', 'green', 'purple', 'orange', 'red', 'yellow'][acc.length % 6]}-100 text-${['blue', 'green', 'purple', 'orange', 'red', 'yellow'][acc.length % 6]}-800`,
      });
    }
    return acc;
  }, []);

  // Create material categories based on actual data
  const materialCategories = [
    {
      id: "document",
      name: "Documentos",
      icon: FileText,
      count: materials.filter(m => m.type === 'document').length
    },
    {
      id: "image",
      name: "Imagens",
      icon: Presentation,
      count: materials.filter(m => m.type === 'image').length
    },
    {
      id: "audio",
      name: "Áudios",
      icon: BookOpen,
      count: materials.filter(m => m.type === 'audio').length
    },
  ];

  const recentMaterials = materials
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    .slice(0, 10);

  const favoriteMaterials = materials.filter(material => favorites.has(material.id));

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
        <Header title="StudyHub" />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">Carregando materiais...</p>
          </div>
        </div>
      </div>
    );
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header title="StudyHub" />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Apostilas e Slides</h2>
          <p className="text-muted-foreground">
            Acesse todos os materiais de estudo organizados por disciplina
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar apostilas, slides ou exercícios..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="space-y-6">
            {/* Categories */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tipos de Material</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {materialCategories.map((category) => {
                  const Icon = category.icon;
                  return (
                    <div
                      key={category.id}
                      className="flex items-center justify-between p-2 rounded-lg hover:bg-muted cursor-pointer"
                    >
                      <div className="flex items-center space-x-2">
                        <Icon className="h-4 w-4 text-brain-600" />
                        <span className="text-sm">{category.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="text-xs">
                          {category.count}
                        </Badge>
                        <ChevronRight className="h-3 w-3 text-muted-foreground" />
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            {/* Folders by Subject */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Por Disciplina</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {folders.map((folder) => (
                  <div
                    key={folder.id}
                    className="p-3 rounded-lg border hover:bg-muted/50 cursor-pointer"
                  >
                    <div className="flex items-center space-x-3">
                      <FolderOpen className="h-5 w-5 text-brain-600" />
                      <div className="flex-1">
                        <h4 className="text-sm font-medium">{folder.name}</h4>
                        <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
                          <span>{folder.itemCount} arquivos</span>
                          <span>{folder.lastUpdated}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Estatísticas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Total de arquivos:</span>
                  <span className="font-semibold">{materials.length}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Downloads totais:</span>
                  <span className="font-semibold">
                    {materials.reduce((sum, m) => sum + (m.downloads || 0), 0)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Favoritos:</span>
                  <span className="font-semibold">{favorites.size}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            <Tabs defaultValue="recentes" className="space-y-6">
              <TabsList>
                <TabsTrigger value="recentes">Recentes</TabsTrigger>
                <TabsTrigger value="favoritos">Favoritos</TabsTrigger>
                <TabsTrigger value="mais-baixados">Mais Baixados</TabsTrigger>
                <TabsTrigger value="por-disciplina">Por Disciplina</TabsTrigger>
              </TabsList>

              <TabsContent value="recentes">
                {recentMaterials.length > 0 ? (
                  <div className="space-y-4">
                    {recentMaterials.map((material) => (
                      <Card
                        key={material.id}
                        className="hover:shadow-md transition-shadow"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="p-3 bg-brain-50 rounded-lg">
                                {getTypeIcon(material.type)}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <h3 className="font-semibold text-lg">
                                    {material.title}
                                  </h3>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => toggleFavorite(material.id)}
                                  >
                                    <Star
                                      className={`h-4 w-4 ${
                                        favorites.has(material.id)
                                          ? 'fill-yellow-400 text-yellow-400'
                                          : 'text-gray-400'
                                      }`}
                                    />
                                  </Button>
                                </div>
                                <p className="text-sm text-muted-foreground mb-2">
                                  StudyHub • {material.course?.name || 'Curso'}
                                </p>
                                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                  <Badge variant="outline">{getTypeName(material.type)}</Badge>
                                  <span>💾 {formatFileSize(material.file_size)}</span>
                                  <span>⬇️ {material.downloads || 0} downloads</span>
                                  <span>📅 {new Date(material.created_at).toLocaleDateString('pt-BR')}</span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">

                              <Button
                                size="sm"
                                className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                                onClick={() => handleViewMaterial(material)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                Visualizar
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">
                        Nenhum material disponível
                      </h3>
                      <p className="text-muted-foreground">
                        Novos materiais serão adicionados em breve
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="favoritos">
                {favoriteMaterials.length > 0 ? (
                  <div className="space-y-4">
                    {favoriteMaterials.map((material) => (
                      <Card
                        key={material.id}
                        className="hover:shadow-md transition-shadow"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="p-3 bg-yellow-50 rounded-lg">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-lg mb-1">
                                  {material.title}
                                </h3>
                                <p className="text-sm text-muted-foreground mb-2">
                                  StudyHub • {material.course?.name || 'Curso'}
                                </p>
                                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                  <Badge variant="outline">
                                    {getTypeName(material.type)}
                                  </Badge>
                                  <span>💾 {formatFileSize(material.file_size)}</span>
                                  <span>⬇️ {material.downloads || 0} downloads</span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">

                              <Button
                                size="sm"
                                className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                                onClick={() => handleViewMaterial(material)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                Visualizar
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Star className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">
                        Nenhum favorito ainda
                      </h3>
                      <p className="text-muted-foreground">
                        Marque materiais como favoritos para acessá-los rapidamente
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="mais-baixados">
                <Card>
                  <CardContent className="p-8 text-center">
                    <Download className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">
                      Materiais Mais Baixados
                    </h3>
                    <p className="text-muted-foreground">
                      Os materiais mais populares da plataforma
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="por-disciplina">
                <div className="grid md:grid-cols-2 gap-6">
                  {folders.map((folder) => (
                    <Card
                      key={folder.id}
                      className="hover:shadow-md transition-shadow cursor-pointer"
                    >
                      <CardHeader>
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${folder.color}`}>
                            <FolderOpen className="h-5 w-5" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">
                              {folder.name}
                            </CardTitle>
                            <CardDescription>
                              {folder.itemCount} arquivos • Atualizado{" "}
                              {folder.lastUpdated}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <Button
                          variant="outline"
                          className="w-full justify-center"
                        >
                          Abrir Pasta
                          <ChevronRight className="h-4 w-4 ml-2" />
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Secure File Viewer */}
      <SecureFileViewer
        isOpen={viewerModal.isOpen}
        onClose={() => setViewerModal(prev => ({ ...prev, isOpen: false }))}
        filePath={viewerModal.filePath}
        fileName={viewerModal.fileName}
        fileType={viewerModal.fileType}
        title={viewerModal.title}
      />
    </div>
  );
};

export default Materials;
