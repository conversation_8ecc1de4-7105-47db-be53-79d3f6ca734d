import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Search,
  Download,
  Eye,
  FileText,
  Presentation,
  BookOpen,
  Star,
  Calendar,
  FolderOpen,
  Filter,
  ChevronRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";

const Materials = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");

  const materialCategories = [
    { id: "apostilas", name: "Apostilas", icon: BookOpen, count: 45 },
    { id: "slides", name: "Slide<PERSON>", icon: Presentation, count: 32 },
    { id: "exercicio<PERSON>", name: "<PERSON>er<PERSON><PERSON><PERSON><PERSON>", icon: FileText, count: 28 },
    { id: "provas", name: "<PERSON><PERSON> Anteriores", icon: FileText, count: 15 },
  ];

  const folders = [
    {
      id: 1,
      name: "Matemática Financeira",
      itemCount: 12,
      lastUpdated: "2 dias atrás",
      color: "bg-blue-100 text-blue-800",
    },
    {
      id: 2,
      name: "Contabilidade Geral",
      itemCount: 8,
      lastUpdated: "1 semana atrás",
      color: "bg-green-100 text-green-800",
    },
    {
      id: 3,
      name: "Gestão de Projetos",
      itemCount: 15,
      lastUpdated: "3 dias atrás",
      color: "bg-purple-100 text-purple-800",
    },
    {
      id: 4,
      name: "Marketing Digital",
      itemCount: 6,
      lastUpdated: "5 dias atrás",
      color: "bg-orange-100 text-orange-800",
    },
  ];

  const recentMaterials = [
    {
      id: 1,
      title: "Apostila - Juros Compostos e Anuidades",
      type: "Apostila",
      subject: "Matemática Financeira",
      professor: "Prof. João Silva",
      size: "2.3 MB",
      pages: 45,
      downloadCount: 234,
      uploadDate: "2 dias atrás",
      isFavorite: true,
    },
    {
      id: 2,
      title: "Slides - Demonstrações Contábeis",
      type: "Slides",
      subject: "Contabilidade",
      professor: "Profa. Ana Costa",
      size: "1.8 MB",
      pages: 28,
      downloadCount: 156,
      uploadDate: "1 semana atrás",
      isFavorite: false,
    },
    {
      id: 3,
      title: "Lista de Exercícios - Scrum e Kanban",
      type: "Exercícios",
      subject: "Gestão de Projetos",
      professor: "Prof. Carlos Mendes",
      size: "890 KB",
      pages: 12,
      downloadCount: 89,
      uploadDate: "3 dias atrás",
      isFavorite: false,
    },
    {
      id: 4,
      title: "Prova - Marketing de Conteúdo 2023",
      type: "Prova",
      subject: "Marketing Digital",
      professor: "Prof. Pedro Lima",
      size: "650 KB",
      pages: 8,
      downloadCount: 67,
      uploadDate: "1 mês atrás",
      isFavorite: true,
    },
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Apostila":
        return <BookOpen className="h-4 w-4" />;
      case "Slides":
        return <Presentation className="h-4 w-4" />;
      case "Exercícios":
      case "Prova":
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header title="StudyHub" />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Apostilas e Slides</h2>
          <p className="text-muted-foreground">
            Acesse todos os materiais de estudo organizados por disciplina
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar apostilas, slides ou exercícios..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="space-y-6">
            {/* Categories */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tipos de Material</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {materialCategories.map((category) => {
                  const Icon = category.icon;
                  return (
                    <div
                      key={category.id}
                      className="flex items-center justify-between p-2 rounded-lg hover:bg-muted cursor-pointer"
                    >
                      <div className="flex items-center space-x-2">
                        <Icon className="h-4 w-4 text-brain-600" />
                        <span className="text-sm">{category.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="text-xs">
                          {category.count}
                        </Badge>
                        <ChevronRight className="h-3 w-3 text-muted-foreground" />
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            {/* Folders by Subject */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Por Disciplina</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {folders.map((folder) => (
                  <div
                    key={folder.id}
                    className="p-3 rounded-lg border hover:bg-muted/50 cursor-pointer"
                  >
                    <div className="flex items-center space-x-3">
                      <FolderOpen className="h-5 w-5 text-brain-600" />
                      <div className="flex-1">
                        <h4 className="text-sm font-medium">{folder.name}</h4>
                        <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
                          <span>{folder.itemCount} arquivos</span>
                          <span>{folder.lastUpdated}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Estatísticas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Total de arquivos:</span>
                  <span className="font-semibold">120</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Downloads este mês:</span>
                  <span className="font-semibold">45</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Favoritos:</span>
                  <span className="font-semibold">8</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            <Tabs defaultValue="recentes" className="space-y-6">
              <TabsList>
                <TabsTrigger value="recentes">Recentes</TabsTrigger>
                <TabsTrigger value="favoritos">Favoritos</TabsTrigger>
                <TabsTrigger value="mais-baixados">Mais Baixados</TabsTrigger>
                <TabsTrigger value="por-disciplina">Por Disciplina</TabsTrigger>
              </TabsList>

              <TabsContent value="recentes">
                <div className="space-y-4">
                  {recentMaterials.map((material) => (
                    <Card
                      key={material.id}
                      className="hover:shadow-md transition-shadow"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="p-3 bg-brain-50 rounded-lg">
                              {getTypeIcon(material.type)}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <h3 className="font-semibold text-lg">
                                  {material.title}
                                </h3>
                                {material.isFavorite && (
                                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mb-2">
                                {material.professor} • {material.subject}
                              </p>
                              <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                <Badge variant="outline">{material.type}</Badge>
                                <span>📄 {material.pages} páginas</span>
                                <span>💾 {material.size}</span>
                                <span>
                                  ⬇️ {material.downloadCount} downloads
                                </span>
                                <span>📅 {material.uploadDate}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4 mr-1" />
                              Visualizar
                            </Button>
                            <Button
                              size="sm"
                              className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Baixar
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="favoritos">
                <div className="space-y-4">
                  {recentMaterials
                    .filter((material) => material.isFavorite)
                    .map((material) => (
                      <Card
                        key={material.id}
                        className="hover:shadow-md transition-shadow"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="p-3 bg-yellow-50 rounded-lg">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-lg mb-1">
                                  {material.title}
                                </h3>
                                <p className="text-sm text-muted-foreground mb-2">
                                  {material.professor} • {material.subject}
                                </p>
                                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                  <Badge variant="outline">
                                    {material.type}
                                  </Badge>
                                  <span>📄 {material.pages} páginas</span>
                                  <span>💾 {material.size}</span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye className="h-4 w-4 mr-1" />
                                Visualizar
                              </Button>
                              <Button
                                size="sm"
                                className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                              >
                                <Download className="h-4 w-4 mr-1" />
                                Baixar
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </TabsContent>

              <TabsContent value="mais-baixados">
                <Card>
                  <CardContent className="p-8 text-center">
                    <Download className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">
                      Materiais Mais Baixados
                    </h3>
                    <p className="text-muted-foreground">
                      Os materiais mais populares da plataforma
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="por-disciplina">
                <div className="grid md:grid-cols-2 gap-6">
                  {folders.map((folder) => (
                    <Card
                      key={folder.id}
                      className="hover:shadow-md transition-shadow cursor-pointer"
                    >
                      <CardHeader>
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${folder.color}`}>
                            <FolderOpen className="h-5 w-5" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">
                              {folder.name}
                            </CardTitle>
                            <CardDescription>
                              {folder.itemCount} arquivos • Atualizado{" "}
                              {folder.lastUpdated}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <Button
                          variant="outline"
                          className="w-full justify-center"
                        >
                          Abrir Pasta
                          <ChevronRight className="h-4 w-4 ml-2" />
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Materials;
