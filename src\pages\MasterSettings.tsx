import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Brain,
  ArrowLeft,
  Settings,
  Users,
  Shield,
  Database,
  Mail,
  Bell,
  Globe,
  Palette,
  Key,
  Download,
  Upload,
  Trash2,
  Save,
  AlertCircle,
  CheckCircle,
  Info,
  Server,
  CreditCard,
  FileText,
  Video,
  Calendar,
} from "lucide-react";
import { getCurrentUser, getUserProfile, updateUserProfile } from "@/lib/supabase";

interface MasterProfile {
  id: string;
  name: string;
  email: string;
  user_type: string;
  status: string;
  created_at: string;
  phone?: string;
}

interface PlatformSettings {
  siteName: string;
  siteDescription: string;
  allowRegistrations: boolean;
  requireApproval: boolean;
  maxStudentsPerClass: number;
  maxFileSize: number;
  allowedFileTypes: string[];
  maintenanceMode: boolean;
  backupFrequency: string;
}

interface NotificationSettings {
  emailNotifications: boolean;
  newStudentAlert: boolean;
  paymentAlert: boolean;
  systemAlert: boolean;
  weeklyReport: boolean;
  monthlyReport: boolean;
}

const MasterSettings = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [currentUser, setCurrentUser] = useState<MasterProfile | null>(null);
  const [saveMessage, setSaveMessage] = useState("");

  // Profile settings
  const [profile, setProfile] = useState({
    name: "",
    email: "",
    phone: "",
    bio: "",
  });

  // Platform settings
  const [platformSettings, setPlatformSettings] = useState<PlatformSettings>({
    siteName: "StudyHub",
    siteDescription: "Plataforma de estudos online",
    allowRegistrations: true,
    requireApproval: true,
    maxStudentsPerClass: 50,
    maxFileSize: 100, // MB
    allowedFileTypes: ["pdf", "doc", "docx", "ppt", "pptx", "mp4", "mp3"],
    maintenanceMode: false,
    backupFrequency: "daily",
  });

  // Notification settings
  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    newStudentAlert: true,
    paymentAlert: true,
    systemAlert: true,
    weeklyReport: true,
    monthlyReport: false,
  });

  // Security settings
  const [security, setSecurity] = useState({
    twoFactorEnabled: false,
    sessionTimeout: 60, // minutes
    passwordExpiry: 90, // days
    loginAttempts: 5,
  });

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const user = await getCurrentUser();
      
      if (!user) {
        navigate("/login");
        return;
      }

      const userProfile = await getUserProfile(user.id);
      
      if (!userProfile || userProfile.user_type !== "master") {
        navigate("/dashboard");
        return;
      }

      setCurrentUser(userProfile);
      setProfile({
        name: userProfile.name || "",
        email: userProfile.email || "",
        phone: userProfile.phone || "",
        bio: "",
      });
    } catch (error) {
      console.error("Error loading user data:", error);
      navigate("/login");
    } finally {
      setIsLoading(false);
    }
  };

  const handleProfileSave = async () => {
    if (!currentUser) return;
    
    setIsSaving(true);
    try {
      await updateUserProfile(currentUser.id, {
        name: profile.name,
        phone: profile.phone,
      });
      
      setSaveMessage("Perfil atualizado com sucesso!");
      setTimeout(() => setSaveMessage(""), 3000);
    } catch (error) {
      console.error("Error updating profile:", error);
      setSaveMessage("Erro ao atualizar perfil");
    } finally {
      setIsSaving(false);
    }
  };

  const handlePlatformSettingsSave = () => {
    // In a real app, this would save to database
    setSaveMessage("Configurações da plataforma salvas!");
    setTimeout(() => setSaveMessage(""), 3000);
  };

  const handleNotificationChange = (key: keyof NotificationSettings, value: boolean) => {
    setNotifications(prev => ({ ...prev, [key]: value }));
  };

  const handleSecurityChange = (key: string, value: any) => {
    setSecurity(prev => ({ ...prev, [key]: value }));
  };

  const handleBackup = () => {
    // Simulate backup process
    setSaveMessage("Backup iniciado! Você receberá um email quando concluído.");
    setTimeout(() => setSaveMessage(""), 5000);
  };

  const handleExportData = (type: string) => {
    // Simulate data export
    setSaveMessage(`Exportação de ${type} iniciada! Download será disponibilizado em breve.`);
    setTimeout(() => setSaveMessage(""), 5000);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Carregando configurações...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      {/* Header */}
      <header className="border-b bg-card/95 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button variant="ghost" onClick={() => navigate("/master")}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
              <div className="w-8 h-8 bg-gradient-to-br from-brain-500 to-cognitive-500 rounded-lg flex items-center justify-center">
                <Settings className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold">Configurações Master</h1>
                <p className="text-sm text-muted-foreground">
                  Gerencie configurações da plataforma
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">Master</Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {saveMessage && (
          <Alert className="mb-6">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{saveMessage}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="profile">Perfil</TabsTrigger>
            <TabsTrigger value="platform">Plataforma</TabsTrigger>
            <TabsTrigger value="notifications">Notificações</TabsTrigger>
            <TabsTrigger value="security">Segurança</TabsTrigger>
            <TabsTrigger value="backup">Backup</TabsTrigger>
            <TabsTrigger value="advanced">Avançado</TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5" />
                  <span>Perfil do Administrador</span>
                </CardTitle>
                <CardDescription>
                  Gerencie suas informações pessoais
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nome Completo</Label>
                    <Input
                      id="name"
                      value={profile.name}
                      onChange={(e) => setProfile(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Seu nome completo"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      value={profile.email}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-muted-foreground">
                      Email não pode ser alterado
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      value={profile.phone}
                      onChange={(e) => setProfile(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="(11) 99999-9999"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="bio">Biografia</Label>
                  <Textarea
                    id="bio"
                    value={profile.bio}
                    onChange={(e) => setProfile(prev => ({ ...prev, bio: e.target.value }))}
                    placeholder="Conte um pouco sobre você..."
                    rows={3}
                  />
                </div>

                <Button 
                  onClick={handleProfileSave} 
                  disabled={isSaving}
                  className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? "Salvando..." : "Salvar Perfil"}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Platform Settings Tab */}
          <TabsContent value="platform">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Globe className="h-5 w-5" />
                    <span>Configurações Gerais</span>
                  </CardTitle>
                  <CardDescription>
                    Configure as informações básicas da plataforma
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="siteName">Nome da Plataforma</Label>
                      <Input
                        id="siteName"
                        value={platformSettings.siteName}
                        onChange={(e) => setPlatformSettings(prev => ({ ...prev, siteName: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxStudents">Máx. Alunos por Turma</Label>
                      <Input
                        id="maxStudents"
                        type="number"
                        value={platformSettings.maxStudentsPerClass}
                        onChange={(e) => setPlatformSettings(prev => ({ ...prev, maxStudentsPerClass: parseInt(e.target.value) }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="siteDescription">Descrição da Plataforma</Label>
                    <Textarea
                      id="siteDescription"
                      value={platformSettings.siteDescription}
                      onChange={(e) => setPlatformSettings(prev => ({ ...prev, siteDescription: e.target.value }))}
                      rows={3}
                    />
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label>Permitir Novos Registros</Label>
                        <p className="text-sm text-muted-foreground">
                          Permite que novos alunos se registrem na plataforma
                        </p>
                      </div>
                      <Switch
                        checked={platformSettings.allowRegistrations}
                        onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, allowRegistrations: checked }))}
                      />
                    </div>
                    <Separator />

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label>Requer Aprovação Manual</Label>
                        <p className="text-sm text-muted-foreground">
                          Novos alunos precisam ser aprovados manualmente
                        </p>
                      </div>
                      <Switch
                        checked={platformSettings.requireApproval}
                        onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, requireApproval: checked }))}
                      />
                    </div>
                    <Separator />

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label>Modo Manutenção</Label>
                        <p className="text-sm text-muted-foreground">
                          Bloqueia acesso de alunos à plataforma
                        </p>
                      </div>
                      <Switch
                        checked={platformSettings.maintenanceMode}
                        onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, maintenanceMode: checked }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>Configurações de Arquivos</span>
                  </CardTitle>
                  <CardDescription>
                    Gerencie uploads e tipos de arquivo permitidos
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="maxFileSize">Tamanho Máximo de Arquivo (MB)</Label>
                    <Input
                      id="maxFileSize"
                      type="number"
                      value={platformSettings.maxFileSize}
                      onChange={(e) => setPlatformSettings(prev => ({ ...prev, maxFileSize: parseInt(e.target.value) }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Tipos de Arquivo Permitidos</Label>
                    <div className="flex flex-wrap gap-2">
                      {platformSettings.allowedFileTypes.map((type) => (
                        <Badge key={type} variant="secondary">
                          .{type}
                        </Badge>
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Para alterar tipos permitidos, entre em contato com o suporte técnico
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Button
                onClick={handlePlatformSettingsSave}
                className="bg-gradient-to-r from-brain-500 to-cognitive-500"
              >
                <Save className="h-4 w-4 mr-2" />
                Salvar Configurações da Plataforma
              </Button>
            </div>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Bell className="h-5 w-5" />
                  <span>Configurações de Notificação</span>
                </CardTitle>
                <CardDescription>
                  Configure quando e como receber notificações
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Notificações por Email</Label>
                      <p className="text-sm text-muted-foreground">
                        Receba notificações importantes por email
                      </p>
                    </div>
                    <Switch
                      checked={notifications.emailNotifications}
                      onCheckedChange={(checked) => handleNotificationChange("emailNotifications", checked)}
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Alerta de Novo Aluno</Label>
                      <p className="text-sm text-muted-foreground">
                        Notificação quando um novo aluno se registra
                      </p>
                    </div>
                    <Switch
                      checked={notifications.newStudentAlert}
                      onCheckedChange={(checked) => handleNotificationChange("newStudentAlert", checked)}
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Alerta de Pagamento</Label>
                      <p className="text-sm text-muted-foreground">
                        Notificação sobre pagamentos e transações
                      </p>
                    </div>
                    <Switch
                      checked={notifications.paymentAlert}
                      onCheckedChange={(checked) => handleNotificationChange("paymentAlert", checked)}
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Alertas do Sistema</Label>
                      <p className="text-sm text-muted-foreground">
                        Notificações sobre problemas técnicos e manutenção
                      </p>
                    </div>
                    <Switch
                      checked={notifications.systemAlert}
                      onCheckedChange={(checked) => handleNotificationChange("systemAlert", checked)}
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Relatório Semanal</Label>
                      <p className="text-sm text-muted-foreground">
                        Resumo semanal de atividades da plataforma
                      </p>
                    </div>
                    <Switch
                      checked={notifications.weeklyReport}
                      onCheckedChange={(checked) => handleNotificationChange("weeklyReport", checked)}
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Relatório Mensal</Label>
                      <p className="text-sm text-muted-foreground">
                        Relatório detalhado mensal com métricas completas
                      </p>
                    </div>
                    <Switch
                      checked={notifications.monthlyReport}
                      onCheckedChange={(checked) => handleNotificationChange("monthlyReport", checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Configurações de Segurança</span>
                  </CardTitle>
                  <CardDescription>
                    Gerencie configurações de segurança e acesso
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label>Autenticação de Dois Fatores</Label>
                        <p className="text-sm text-muted-foreground">
                          Adiciona uma camada extra de segurança ao login
                        </p>
                      </div>
                      <Switch
                        checked={security.twoFactorEnabled}
                        onCheckedChange={(checked) => handleSecurityChange("twoFactorEnabled", checked)}
                      />
                    </div>
                    <Separator />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="sessionTimeout">Timeout de Sessão (minutos)</Label>
                      <Input
                        id="sessionTimeout"
                        type="number"
                        value={security.sessionTimeout}
                        onChange={(e) => handleSecurityChange("sessionTimeout", parseInt(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="loginAttempts">Máx. Tentativas de Login</Label>
                      <Input
                        id="loginAttempts"
                        type="number"
                        value={security.loginAttempts}
                        onChange={(e) => handleSecurityChange("loginAttempts", parseInt(e.target.value))}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="passwordExpiry">Expiração de Senha (dias)</Label>
                    <Input
                      id="passwordExpiry"
                      type="number"
                      value={security.passwordExpiry}
                      onChange={(e) => handleSecurityChange("passwordExpiry", parseInt(e.target.value))}
                    />
                    <p className="text-sm text-muted-foreground">
                      0 = nunca expira
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Key className="h-5 w-5" />
                    <span>Gerenciamento de Senhas</span>
                  </CardTitle>
                  <CardDescription>
                    Altere sua senha de administrador
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Senha Atual</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      placeholder="Digite sua senha atual"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">Nova Senha</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      placeholder="Digite a nova senha"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirmar Nova Senha</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder="Confirme a nova senha"
                    />
                  </div>
                  <Button variant="outline">
                    <Key className="h-4 w-4 mr-2" />
                    Alterar Senha
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Backup Tab */}
          <TabsContent value="backup">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Database className="h-5 w-5" />
                    <span>Backup e Restauração</span>
                  </CardTitle>
                  <CardDescription>
                    Gerencie backups dos dados da plataforma
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label>Frequência de Backup Automático</Label>
                    <select
                      value={platformSettings.backupFrequency}
                      onChange={(e) => setPlatformSettings(prev => ({ ...prev, backupFrequency: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="daily">Diário</option>
                      <option value="weekly">Semanal</option>
                      <option value="monthly">Mensal</option>
                      <option value="disabled">Desabilitado</option>
                    </select>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Backup Manual</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button
                        variant="outline"
                        onClick={handleBackup}
                        className="justify-start"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Backup Completo
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleBackup()}
                        className="justify-start"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Backup Apenas Dados
                      </Button>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Restaurar Backup</h4>
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                      <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground mb-2">
                        Arraste um arquivo de backup aqui ou clique para selecionar
                      </p>
                      <Button variant="outline" size="sm">
                        Selecionar Arquivo
                      </Button>
                    </div>
                  </div>

                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Atenção:</strong> A restauração de backup substituirá todos os dados atuais.
                      Esta ação não pode ser desfeita.
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Advanced Tab */}
          <TabsContent value="advanced">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Server className="h-5 w-5" />
                    <span>Configurações Avançadas</span>
                  </CardTitle>
                  <CardDescription>
                    Configurações técnicas e de sistema
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Cuidado:</strong> Estas configurações são para usuários avançados.
                      Alterações incorretas podem afetar o funcionamento da plataforma.
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-4">
                    <h4 className="font-medium">Exportar Dados</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button
                        variant="outline"
                        onClick={() => handleExportData("usuários")}
                        className="justify-start"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Exportar Usuários (CSV)
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleExportData("conteúdo")}
                        className="justify-start"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Exportar Conteúdo (JSON)
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleExportData("logs")}
                        className="justify-start"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Exportar Logs (TXT)
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleExportData("analytics")}
                        className="justify-start"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Exportar Analytics (XLSX)
                      </Button>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium text-destructive">Zona de Perigo</h4>
                    <div className="border border-destructive/20 rounded-lg p-4 space-y-4">
                      <div className="space-y-2">
                        <Label className="text-destructive">Limpar Cache do Sistema</Label>
                        <p className="text-sm text-muted-foreground">
                          Remove todos os arquivos temporários e cache
                        </p>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Limpar Cache
                        </Button>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <Label className="text-destructive">Reset Completo da Plataforma</Label>
                        <p className="text-sm text-muted-foreground">
                          Remove todos os dados e restaura configurações padrão
                        </p>
                        <Button variant="destructive" size="sm" disabled>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Reset Completo (Desabilitado)
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MasterSettings;
