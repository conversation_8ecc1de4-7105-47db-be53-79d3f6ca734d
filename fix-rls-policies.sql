-- Fix RLS policies to allow profile creation during signup
-- Run this in your Supabase SQL Editor

-- First, drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Masters can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Masters can update student profiles" ON profiles;
DROP POLICY IF EXISTS "Allow profile creation" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;

-- Create new, more permissive policies for profile creation

-- 1. Allow users to view their own profile
CREATE POLICY "Users can view own profile" ON profiles 
  FOR SELECT USING (auth.uid() = id);

-- 2. Allow users to update their own profile
CREATE POLICY "Users can update own profile" ON profiles 
  FOR UPDATE USING (auth.uid() = id);

-- 3. Allow masters to view all profiles
CREATE POLICY "Masters can view all profiles" ON profiles 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND user_type IN ('master', 'admin')
    )
  );

-- 4. Allow masters to update student profiles
CREATE POLICY "Masters can update student profiles" ON profiles 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND user_type IN ('master', 'admin')
    )
  );

-- 5. CRITICAL: Allow profile creation for authenticated users
-- This is the key policy that was missing/incorrect
CREATE POLICY "Allow profile creation during signup" ON profiles 
  FOR INSERT WITH CHECK (
    auth.uid() = id AND auth.uid() IS NOT NULL
  );

-- 6. Alternative: If the above doesn't work, try this more permissive policy
-- Uncomment this if you still have issues:
-- CREATE POLICY "Allow all profile creation" ON profiles 
--   FOR INSERT WITH CHECK (true);

-- 7. Ensure RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 8. Grant necessary permissions
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON profiles TO anon;

-- 9. Refresh the schema cache
NOTIFY pgrst, 'reload schema';
