import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Brain,
  User,
  Bell,
  Shield,
  Palette,
  Globe,
  Download,
  Trash2,
  Eye,
  Camera,
  Save,
  LogOut,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { signOut } from "@/lib/supabase";

const Settings = () => {
  const navigate = useNavigate();
  const [profile, setProfile] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    course: "Administração",
    semester: "5º período",
    phone: "(11) 99999-9999",
    bio: "Estudante de Administração focado em gestão financeira e empreendedorismo.",
  });

  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    pushNotifications: true,
    classReminders: true,
    newMaterials: true,
    chatMessages: false,
    weeklyDigest: true,
    assignmentDeadlines: true,
  });

  const [preferences, setPreferences] = useState({
    theme: "system",
    language: "pt-BR",
    timezone: "America/Sao_Paulo",
    autoDownload: false,
    videoQuality: "hd",
    chatSounds: true,
  });

  const handleLogout = async () => {
    try {
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  const Header = () => (
    <header className="border-b bg-card/95 backdrop-blur-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-brain-500 to-cognitive-500 rounded-lg flex items-center justify-center">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-xl font-semibold">StudyHub</h1>
          </div>
          <Button variant="ghost" onClick={() => navigate("/dashboard")}>
            Voltar ao Dashboard
          </Button>
        </div>
      </div>
    </header>
  );

  const handleProfileUpdate = () => {
    // Aqui seria feita a atualização do perfil
    console.log("Atualizando perfil:", profile);
  };

  const handleNotificationChange = (key: string, value: boolean) => {
    setNotifications((prev) => ({ ...prev, [key]: value }));
  };

  const handlePreferenceChange = (key: string, value: string | boolean) => {
    setPreferences((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Configurações</h2>
          <p className="text-muted-foreground">
            Gerencie suas preferências pessoais e configurações da conta
          </p>
        </div>

        <Tabs defaultValue="perfil" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="perfil">Perfil</TabsTrigger>
            <TabsTrigger value="notificacoes">Notificações</TabsTrigger>
            <TabsTrigger value="preferencias">Preferências</TabsTrigger>
            <TabsTrigger value="privacidade">Privacidade</TabsTrigger>
            <TabsTrigger value="conta">Conta</TabsTrigger>
          </TabsList>

          <TabsContent value="perfil">
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <User className="h-5 w-5" />
                      <span>Informações Pessoais</span>
                    </CardTitle>
                    <CardDescription>
                      Atualize suas informações pessoais e acadêmicas
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Nome Completo</Label>
                        <Input
                          id="name"
                          value={profile.name}
                          onChange={(e) =>
                            setProfile({ ...profile, name: e.target.value })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">E-mail</Label>
                        <Input
                          id="email"
                          type="email"
                          value={profile.email}
                          onChange={(e) =>
                            setProfile({ ...profile, email: e.target.value })
                          }
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="course">Curso</Label>
                        <Input
                          id="course"
                          value={profile.course}
                          onChange={(e) =>
                            setProfile({ ...profile, course: e.target.value })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="semester">Período/Semestre</Label>
                        <Input
                          id="semester"
                          value={profile.semester}
                          onChange={(e) =>
                            setProfile({ ...profile, semester: e.target.value })
                          }
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">Telefone</Label>
                      <Input
                        id="phone"
                        value={profile.phone}
                        onChange={(e) =>
                          setProfile({ ...profile, phone: e.target.value })
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bio">Biografia</Label>
                      <textarea
                        id="bio"
                        rows={4}
                        className="w-full px-3 py-2 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                        value={profile.bio}
                        onChange={(e) =>
                          setProfile({ ...profile, bio: e.target.value })
                        }
                        placeholder="Conte um pouco sobre você..."
                      />
                    </div>

                    <Button
                      onClick={handleProfileUpdate}
                      className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Salvar Alterações
                    </Button>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Foto do Perfil</CardTitle>
                    <CardDescription>
                      Sua foto aparecerá no chat e no perfil
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex flex-col items-center space-y-4">
                      <Avatar className="h-24 w-24">
                        <AvatarFallback className="text-2xl bg-gradient-to-br from-brain-100 to-cognitive-100 text-brain-700">
                          {profile.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-2">
                        <Button size="sm" variant="outline">
                          <Camera className="h-4 w-4 mr-2" />
                          Alterar Foto
                        </Button>
                        <Button size="sm" variant="ghost" className="w-full">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Remover
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Estatísticas</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Membro desde:</span>
                      <span className="font-medium">Mar 2024</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Aulas assistidas:</span>
                      <span className="font-medium">45</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Materiais baixados:</span>
                      <span className="font-medium">23</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Mensagens enviadas:</span>
                      <span className="font-medium">156</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="notificacoes">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Bell className="h-5 w-5" />
                  <span>Configurações de Notificação</span>
                </CardTitle>
                <CardDescription>
                  Escolha como e quando receber notificações
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Notificações por E-mail</Label>
                      <p className="text-sm text-muted-foreground">
                        Receba atualizações importantes por e-mail
                      </p>
                    </div>
                    <Switch
                      checked={notifications.emailNotifications}
                      onCheckedChange={(checked) =>
                        handleNotificationChange("emailNotifications", checked)
                      }
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Notificações Push</Label>
                      <p className="text-sm text-muted-foreground">
                        Notificações instantâneas no navegador
                      </p>
                    </div>
                    <Switch
                      checked={notifications.pushNotifications}
                      onCheckedChange={(checked) =>
                        handleNotificationChange("pushNotifications", checked)
                      }
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Lembretes de Aulas</Label>
                      <p className="text-sm text-muted-foreground">
                        Avisos 15 minutos antes das aulas ao vivo
                      </p>
                    </div>
                    <Switch
                      checked={notifications.classReminders}
                      onCheckedChange={(checked) =>
                        handleNotificationChange("classReminders", checked)
                      }
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Novos Materiais</Label>
                      <p className="text-sm text-muted-foreground">
                        Aviso quando novos materiais forem publicados
                      </p>
                    </div>
                    <Switch
                      checked={notifications.newMaterials}
                      onCheckedChange={(checked) =>
                        handleNotificationChange("newMaterials", checked)
                      }
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Mensagens do Chat</Label>
                      <p className="text-sm text-muted-foreground">
                        Notificações de mensagens diretas e menções
                      </p>
                    </div>
                    <Switch
                      checked={notifications.chatMessages}
                      onCheckedChange={(checked) =>
                        handleNotificationChange("chatMessages", checked)
                      }
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Resumo Semanal</Label>
                      <p className="text-sm text-muted-foreground">
                        Relatório semanal do seu progresso
                      </p>
                    </div>
                    <Switch
                      checked={notifications.weeklyDigest}
                      onCheckedChange={(checked) =>
                        handleNotificationChange("weeklyDigest", checked)
                      }
                    />
                  </div>
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Prazos de Atividades</Label>
                      <p className="text-sm text-muted-foreground">
                        Lembretes de prazos de entrega
                      </p>
                    </div>
                    <Switch
                      checked={notifications.assignmentDeadlines}
                      onCheckedChange={(checked) =>
                        handleNotificationChange("assignmentDeadlines", checked)
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preferencias">
            <div className="grid lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Palette className="h-5 w-5" />
                    <span>Aparência</span>
                  </CardTitle>
                  <CardDescription>
                    Personalize a aparência da plataforma
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label>Tema</Label>
                    <RadioGroup
                      value={preferences.theme}
                      onValueChange={(value) =>
                        handlePreferenceChange("theme", value)
                      }
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="light" id="light" />
                        <Label htmlFor="light">Claro</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="dark" id="dark" />
                        <Label htmlFor="dark">Escuro</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="system" id="system" />
                        <Label htmlFor="system">Automático (sistema)</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label>Qualidade de Vídeo</Label>
                    <RadioGroup
                      value={preferences.videoQuality}
                      onValueChange={(value) =>
                        handlePreferenceChange("videoQuality", value)
                      }
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="sd" id="sd" />
                        <Label htmlFor="sd">SD (480p)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="hd" id="hd" />
                        <Label htmlFor="hd">HD (720p)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="fhd" id="fhd" />
                        <Label htmlFor="fhd">Full HD (1080p)</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Globe className="h-5 w-5" />
                    <span>Região e Idioma</span>
                  </CardTitle>
                  <CardDescription>
                    Configure idioma e configurações regionais
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="language">Idioma</Label>
                    <select
                      id="language"
                      value={preferences.language}
                      onChange={(e) =>
                        handlePreferenceChange("language", e.target.value)
                      }
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="pt-BR">Português (Brasil)</option>
                      <option value="en-US">English (US)</option>
                      <option value="es-ES">Español</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timezone">Fuso Horário</Label>
                    <select
                      id="timezone"
                      value={preferences.timezone}
                      onChange={(e) =>
                        handlePreferenceChange("timezone", e.target.value)
                      }
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="America/Sao_Paulo">
                        (UTC-3) São Paulo
                      </option>
                      <option value="America/New_York">(UTC-5) New York</option>
                      <option value="Europe/London">(UTC+0) London</option>
                    </select>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label>Download Automático</Label>
                        <p className="text-sm text-muted-foreground">
                          Baixar materiais automaticamente
                        </p>
                      </div>
                      <Switch
                        checked={preferences.autoDownload}
                        onCheckedChange={(checked) =>
                          handlePreferenceChange("autoDownload", checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label>Sons do Chat</Label>
                        <p className="text-sm text-muted-foreground">
                          Reproduzir som ao receber mensagens
                        </p>
                      </div>
                      <Switch
                        checked={preferences.chatSounds}
                        onCheckedChange={(checked) =>
                          handlePreferenceChange("chatSounds", checked)
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="privacidade">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Privacidade e Segurança</span>
                </CardTitle>
                <CardDescription>
                  Gerencie suas configurações de privacidade
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Visibilidade do Perfil</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>Mostrar status online</Label>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Permitir mensagens diretas</Label>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Mostrar progresso para colegas</Label>
                      <Switch />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Dados e Privacidade</h4>
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full justify-start">
                      <Download className="h-4 w-4 mr-2" />
                      Baixar meus dados
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Eye className="h-4 w-4 mr-2" />
                      Ver dados coletados
                    </Button>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Segurança da Conta</h4>
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full justify-start">
                      Alterar senha
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Configurar autenticação em duas etapas
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Ver dispositivos conectados
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="conta">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Informações da Conta</CardTitle>
                  <CardDescription>
                    Gerencie sua conta e assinatura
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label>Plano Atual</Label>
                      <p className="text-lg font-semibold text-brain-600">
                        Estudante Premium
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Válido até 31/12/2024
                      </p>
                    </div>
                    <div>
                      <Label>ID da Conta</Label>
                      <p className="text-sm font-mono">STH-2024-0234</p>
                    </div>
                  </div>
                  <Button variant="outline">Gerenciar Assinatura</Button>
                </CardContent>
              </Card>

              <Card className="border-destructive/50">
                <CardHeader>
                  <CardTitle className="text-destructive">
                    Zona de Perigo
                  </CardTitle>
                  <CardDescription>
                    Ações irreversíveis que afetam sua conta
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Desativar Conta</h4>
                    <p className="text-sm text-muted-foreground">
                      Sua conta será desativada temporariamente. Você pode
                      reativá-la a qualquer momento.
                    </p>
                    <Button
                      variant="outline"
                      className="text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
                    >
                      Desativar Conta
                    </Button>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="font-medium">Excluir Conta</h4>
                    <p className="text-sm text-muted-foreground">
                      Esta ação é permanente. Todos os seus dados serão
                      perdidos.
                    </p>
                    <Button variant="destructive">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Excluir Conta Permanentemente
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sair da Conta
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Settings;
