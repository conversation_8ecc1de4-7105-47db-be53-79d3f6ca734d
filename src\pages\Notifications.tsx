import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Bell,
  Check,
  X,
  Calendar,
  BookOpen,
  MessageCircle,
  Users,
  Star,
  AlertCircle,
  Clock,
  Trash2,
  Settings,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";

interface Notification {
  id: number;
  type: "class" | "material" | "chat" | "system" | "assignment";
  title: string;
  message: string;
  time: string;
  isRead: boolean;
  priority: "low" | "normal" | "high";
  actionUrl?: string;
}

const Notifications = () => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      type: "class",
      title: "Aula começando em 15 minutos",
      message: "Matemática Financeira - Juros Compostos com Prof. <PERSON>",
      time: "há 2 minutos",
      isRead: false,
      priority: "high",
      actionUrl: "/live-classes",
    },
    {
      id: 2,
      type: "material",
      title: "Novo material disponível",
      message:
        "Apostila 'Demonstrações Contábeis' foi adicionada em Contabilidade",
      time: "há 30 minutos",
      isRead: false,
      priority: "normal",
      actionUrl: "/materials",
    },
    {
      id: 3,
      type: "chat",
      title: "Nova mensagem no chat",
      message: "Prof. Ana Costa respondeu sua dúvida sobre DRE",
      time: "há 1 hora",
      isRead: false,
      priority: "normal",
      actionUrl: "/chat",
    },
    {
      id: 4,
      type: "assignment",
      title: "Prazo de entrega próximo",
      message: "Lista de exercícios de Gestão de Projetos vence em 2 dias",
      time: "há 2 horas",
      isRead: true,
      priority: "high",
    },
    {
      id: 5,
      type: "system",
      title: "Manutenção programada",
      message: "Sistema ficará indisponível das 2h às 4h para manutenção",
      time: "há 1 dia",
      isRead: true,
      priority: "low",
    },
    {
      id: 6,
      type: "material",
      title: "Vídeo favoritado foi atualizado",
      message: "Aula 'Introdução aos Juros Compostos' foi reformulada",
      time: "há 2 dias",
      isRead: true,
      priority: "low",
      actionUrl: "/videos",
    },
    {
      id: 7,
      type: "chat",
      title: "Menção no chat da turma",
      message: "Pedro Lima mencionou você no canal Matemática Financeira",
      time: "há 3 dias",
      isRead: true,
      priority: "normal",
      actionUrl: "/chat",
    },
  ]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "class":
        return <Calendar className="h-5 w-5 text-blue-600" />;
      case "material":
        return <BookOpen className="h-5 w-5 text-green-600" />;
      case "chat":
        return <MessageCircle className="h-5 w-5 text-purple-600" />;
      case "assignment":
        return <Clock className="h-5 w-5 text-orange-600" />;
      case "system":
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
      default:
        return <Bell className="h-5 w-5 text-gray-600" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge variant="destructive">Alta</Badge>;
      case "normal":
        return <Badge variant="default">Normal</Badge>;
      case "low":
        return <Badge variant="secondary">Baixa</Badge>;
      default:
        return null;
    }
  };

  const markAsRead = (id: number) => {
    setNotifications((prev) =>
      prev.map((notif) =>
        notif.id === id ? { ...notif, isRead: true } : notif,
      ),
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notif) => ({ ...notif, isRead: true })),
    );
  };

  const deleteNotification = (id: number) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id));
  };

  const unreadCount = notifications.filter((n) => !n.isRead).length;
  const todayNotifications = notifications.filter(
    (n) => n.time.includes("minutos") || n.time.includes("hora"),
  );
  const olderNotifications = notifications.filter(
    (n) => !todayNotifications.includes(n),
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header title="StudyHub" />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Notificações</h2>
            <p className="text-muted-foreground">
              {unreadCount > 0
                ? `Você tem ${unreadCount} notificação${unreadCount > 1 ? "ões" : ""} não lida${unreadCount > 1 ? "s" : ""}`
                : "Todas as notificações foram lidas"}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <Button variant="outline" onClick={markAllAsRead}>
                <Check className="h-4 w-4 mr-2" />
                Marcar todas como lidas
              </Button>
            )}
            <Button
              variant="ghost"
              onClick={() => navigate("/settings?tab=notificacoes")}
            >
              <Settings className="h-4 w-4 mr-2" />
              Configurar
            </Button>
          </div>
        </div>

        <Tabs defaultValue="todas" className="space-y-6">
          <TabsList>
            <TabsTrigger value="todas">
              Todas {unreadCount > 0 && `(${unreadCount})`}
            </TabsTrigger>
            <TabsTrigger value="nao-lidas">Não Lidas</TabsTrigger>
            <TabsTrigger value="importantes">Importantes</TabsTrigger>
            <TabsTrigger value="aulas">Aulas</TabsTrigger>
            <TabsTrigger value="materiais">Materiais</TabsTrigger>
          </TabsList>

          <TabsContent value="todas">
            <div className="space-y-6">
              {/* Today's Notifications */}
              {todayNotifications.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Hoje</h3>
                  <div className="space-y-3">
                    {todayNotifications.map((notification) => (
                      <Card
                        key={notification.id}
                        className={`transition-all hover:shadow-md ${
                          !notification.isRead
                            ? "border-l-4 border-l-primary bg-primary/5"
                            : ""
                        }`}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-4">
                            <div className="p-2 bg-muted rounded-lg">
                              {getNotificationIcon(notification.type)}
                            </div>
                            <div className="flex-1 space-y-2">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h4 className="font-semibold">
                                    {notification.title}
                                  </h4>
                                  <p className="text-sm text-muted-foreground">
                                    {notification.message}
                                  </p>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getPriorityBadge(notification.priority)}
                                  {!notification.isRead && (
                                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-muted-foreground">
                                  {notification.time}
                                </span>
                                <div className="flex items-center space-x-2">
                                  {notification.actionUrl && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() =>
                                        navigate(notification.actionUrl!)
                                      }
                                    >
                                      Ver
                                    </Button>
                                  )}
                                  {!notification.isRead && (
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() =>
                                        markAsRead(notification.id)
                                      }
                                    >
                                      <Check className="h-4 w-4" />
                                    </Button>
                                  )}
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() =>
                                      deleteNotification(notification.id)
                                    }
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Older Notifications */}
              {olderNotifications.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Anteriores</h3>
                  <div className="space-y-3">
                    {olderNotifications.map((notification) => (
                      <Card
                        key={notification.id}
                        className={`transition-all hover:shadow-md ${
                          !notification.isRead
                            ? "border-l-4 border-l-primary bg-primary/5"
                            : "opacity-75"
                        }`}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-4">
                            <div className="p-2 bg-muted rounded-lg">
                              {getNotificationIcon(notification.type)}
                            </div>
                            <div className="flex-1 space-y-2">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h4 className="font-semibold">
                                    {notification.title}
                                  </h4>
                                  <p className="text-sm text-muted-foreground">
                                    {notification.message}
                                  </p>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getPriorityBadge(notification.priority)}
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-muted-foreground">
                                  {notification.time}
                                </span>
                                <div className="flex items-center space-x-2">
                                  {notification.actionUrl && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() =>
                                        navigate(notification.actionUrl!)
                                      }
                                    >
                                      Ver
                                    </Button>
                                  )}
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() =>
                                      deleteNotification(notification.id)
                                    }
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="nao-lidas">
            <div className="space-y-3">
              {notifications
                .filter((n) => !n.isRead)
                .map((notification) => (
                  <Card
                    key={notification.id}
                    className="border-l-4 border-l-primary bg-primary/5"
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-4">
                        <div className="p-2 bg-muted rounded-lg">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 space-y-2">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold">
                                {notification.title}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                {notification.message}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              {getPriorityBadge(notification.priority)}
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-muted-foreground">
                              {notification.time}
                            </span>
                            <div className="flex items-center space-x-2">
                              {notification.actionUrl && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() =>
                                    navigate(notification.actionUrl!)
                                  }
                                >
                                  Ver
                                </Button>
                              )}
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => markAsRead(notification.id)}
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="importantes">
            <div className="space-y-3">
              {notifications
                .filter((n) => n.priority === "high")
                .map((notification) => (
                  <Card
                    key={notification.id}
                    className="border-l-4 border-l-destructive"
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-4">
                        <div className="p-2 bg-destructive/10 rounded-lg">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 space-y-2">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold">
                                {notification.title}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                {notification.message}
                              </p>
                            </div>
                            <Badge variant="destructive">Importante</Badge>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {notification.time}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="aulas">
            <div className="space-y-3">
              {notifications
                .filter((n) => n.type === "class")
                .map((notification) => (
                  <Card key={notification.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-4">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          <Calendar className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1 space-y-2">
                          <h4 className="font-semibold">
                            {notification.title}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {notification.message}
                          </p>
                          <span className="text-xs text-muted-foreground">
                            {notification.time}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="materiais">
            <div className="space-y-3">
              {notifications
                .filter((n) => n.type === "material")
                .map((notification) => (
                  <Card key={notification.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-4">
                        <div className="p-2 bg-green-50 rounded-lg">
                          <BookOpen className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="flex-1 space-y-2">
                          <h4 className="font-semibold">
                            {notification.title}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {notification.message}
                          </p>
                          <span className="text-xs text-muted-foreground">
                            {notification.time}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </TabsContent>
        </Tabs>

        {notifications.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <Bell className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">
                Nenhuma notificação
              </h3>
              <p className="text-muted-foreground">
                Quando algo importante acontecer, você será notificado aqui
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default Notifications;
