import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Progress } from "@/components/ui/progress";
import { ConfirmModal, SuccessModal } from "@/components/ui/modal";
import { StudentProfileModal } from "@/components/StudentProfileModal";
import { FinancialManagement } from "@/components/FinancialManagement";
import { SecureFileViewer } from "@/components/SecureFileViewer";
import {
  Brain,
  Upload,
  Users,
  CreditCard,
  FileText,
  Video,
  Calendar,
  Settings,
  BarChart3,
  Download,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Shield,
  X,
  LogOut,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  getPendingStudents,
  getAllStudents,
  updateUserStatus,
  getCurrentUser,
  getUserProfile,
  updateUserProfile,
  getContent,
  getPaymentPlans,
  updateContent,
  deleteContent,
  createContent,
  uploadFile,
  Profile,
  signOut,
} from "@/lib/supabase";

const MasterPanel = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [pendingStudents, setPendingStudents] = useState<Profile[]>([]);
  const [allStudentsData, setAllStudentsData] = useState<Profile[]>([]);
  const [currentUser, setCurrentUser] = useState<Profile | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedContent, setSelectedContent] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Estados para diferentes seções
  const [newContent, setNewContent] = useState({
    title: "",
    description: "",
    category: "",
    type: "video",
    file_path: "",
    file_size: 0,
    file_name: ""
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);

      // Verificar se é um usuário master
      const user = await getCurrentUser();
      if (user) {
        const profile = await getUserProfile(user.id);
        if (!profile || profile.user_type !== "master") {
          // If not a master user, redirect to dashboard
          navigate("/dashboard");
          return;
        }
        setCurrentUser(profile);
      } else {
        // If not logged in, redirect to login
        navigate("/login");
        return;
      }

      // Carregar alunos pendentes, todos os alunos, conteúdo e pagamentos
      const [pending, all, content, payments] = await Promise.all([
        getPendingStudents(),
        getAllStudents(),
        getContent(),
        getPaymentPlans(),
      ]);

      setPendingStudents(pending);
      setAllStudentsData(all);
      setContentData(content);
      setPaymentsData(payments);
    } catch (error) {
      console.error("Erro ao carregar dados:", error);
      // Em caso de erro, redirecionar para o dashboard
      navigate("/dashboard");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproveStudent = async (studentId: string) => {
    const student = allStudentsData.find(s => s.id === studentId);
    setConfirmModal({
      isOpen: true,
      title: "Aprovar Aluno",
      message: `Tem certeza que deseja aprovar o aluno ${student?.name}? O aluno poderá acessar a plataforma.`,
      variant: "default",
      onConfirm: async () => {
        try {
          await updateUserStatus(studentId, "approved", currentUser?.id);
          await loadData();
          setSuccessModal({
            isOpen: true,
            title: "Sucesso",
            message: "Aluno aprovado com sucesso!"
          });
        } catch (error) {
          console.error("Erro ao aprovar aluno:", error);
          setSuccessModal({
            isOpen: true,
            title: "Erro",
            message: "Erro ao aprovar aluno"
          });
        }
      }
    });
  };

  const handleSuspendStudent = async (studentId: string) => {
    const student = allStudentsData.find(s => s.id === studentId);
    setConfirmModal({
      isOpen: true,
      title: "Suspender Aluno",
      message: `Tem certeza que deseja suspender o aluno ${student?.name}?`,
      variant: "destructive",
      onConfirm: async () => {
        try {
          await updateUserStatus(studentId, "suspended", currentUser?.id);
          await loadData();
          setSuccessModal({
            isOpen: true,
            title: "Sucesso",
            message: "Aluno suspenso com sucesso!"
          });
        } catch (error) {
          console.error("Erro ao suspender aluno:", error);
          setSuccessModal({
            isOpen: true,
            title: "Erro",
            message: "Erro ao suspender aluno"
          });
        }
      }
    });
  };

  const handleDeleteContent = async (contentId: string) => {
    setConfirmModal({
      isOpen: true,
      title: "Deletar Conteúdo",
      message: "Tem certeza que deseja deletar este conteúdo? Esta ação não pode ser desfeita.",
      variant: "destructive",
      onConfirm: async () => {
        try {
          await deleteContent(contentId);
          await loadData(); // Recarregar todos os dados
          setSuccessModal({
            isOpen: true,
            title: "Sucesso",
            message: "Conteúdo deletado com sucesso!"
          });
        } catch (error) {
          console.error("Erro ao deletar conteúdo:", error);
          setSuccessModal({
            isOpen: true,
            title: "Erro",
            message: "Erro ao deletar conteúdo"
          });
        }
      }
    });
  };

  const handleEditContent = async (contentId: string) => {
    // Em uma aplicação real, isso abriria um modal de edição com dados do conteúdo
    const content = contentData.find(c => c.id === contentId);
    if (content) {
      const newTitle = prompt("Novo título:", content.title);
      if (newTitle && newTitle !== content.title) {
        try {
          await updateContent(contentId, { title: newTitle });
          await loadData(); // Recarregar todos os dados
          setSuccessModal({
            isOpen: true,
            title: "Sucesso",
            message: "Conteúdo atualizado com sucesso!"
          });
        } catch (error) {
          console.error("Erro ao atualizar conteúdo:", error);
          setSuccessModal({
            isOpen: true,
            title: "Erro",
            message: "Erro ao atualizar conteúdo"
          });
        }
      }
    }
  };

  const handleViewContent = (contentId: string) => {
    const content = contentData.find(c => c.id === contentId);
    if (content && content.file_url) {
      // Abrir visualizador seguro de arquivos
      setViewerModal({
        isOpen: true,
        filePath: content.file_url, // Este é na verdade o caminho do arquivo
        fileName: content.title,
        fileType: content.type,
        title: content.title
      });
    } else {
      // Fallback para modal de detalhes se não houver arquivo
      setSuccessModal({
        isOpen: true,
        title: "Detalhes do Conteúdo",
        message: `Título: ${content?.title || 'N/A'}\nTipo: ${content?.type || 'N/A'}\nStatus: ${content?.status || 'N/A'}\n\nArquivo não disponível para visualização.`
      });
    }
  };

  const handleEditStudent = (studentId: string) => {
    const student = allStudentsData.find(s => s.id === studentId);
    if (student) {
      setStudentModal({
        isOpen: true,
        student: student
      });
    }
  };

  const handleBlockStudent = async (studentId: string) => {
    const student = allStudentsData.find(s => s.id === studentId);
    setConfirmModal({
      isOpen: true,
      title: "Bloquear Aluno",
      message: `Tem certeza que deseja bloquear o aluno ${student?.name}? O aluno não poderá mais acessar a plataforma.`,
      variant: "destructive",
      onConfirm: async () => {
        try {
          await updateUserStatus(studentId, "suspended", currentUser?.id);
          await loadData();
          setSuccessModal({
            isOpen: true,
            title: "Sucesso",
            message: "Aluno bloqueado com sucesso!"
          });
        } catch (error) {
          console.error("Erro ao bloquear aluno:", error);
          setSuccessModal({
            isOpen: true,
            title: "Erro",
            message: "Erro ao bloquear aluno"
          });
        }
      }
    });
  };

  const handleUpdatePaymentStatus = (paymentId: string, newStatus: string) => {
    setPaymentsData(prev =>
      prev.map(payment =>
        payment.id === paymentId
          ? { ...payment, status: newStatus }
          : payment
      )
    );
    setSuccessModal({
      isOpen: true,
      title: "Pagamento Atualizado",
      message: `Status do pagamento atualizado para: ${newStatus}`
    });
  };

  const handleExportReport = (reportType: string) => {
    // Em uma aplicação real, isso geraria e baixaria o relatório
    setSuccessModal({
      isOpen: true,
      title: "Relatório em Processamento",
      message: `Gerando relatório: ${reportType}. Você receberá um email quando estiver pronto para download.`
    });
  };

  const handleToggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Dados reais virão do banco de dados
  const [contentData, setContentData] = useState([]);
  const [paymentsData, setPaymentsData] = useState([]);

  // Estados dos modais
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    title: "",
    message: "",
    onConfirm: () => {},
    variant: "default" as "default" | "destructive"
  });
  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    title: "",
    message: ""
  });
  const [studentModal, setStudentModal] = useState({
    isOpen: false,
    student: null as any
  });
  const [viewerModal, setViewerModal] = useState({
    isOpen: false,
    filePath: "",
    fileName: "",
    fileType: "",
    title: ""
  });

  // Estado da aba
  const [activeTab, setActiveTab] = useState("upload");

  const Header = () => {
    const handleLogout = async () => {
      try {
        await signOut();
        navigate("/login");
      } catch (error) {
        console.error("Error logging out:", error);
      }
    };

    return (
      <header className="border-b bg-card/95 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-brain-500 to-cognitive-500 rounded-lg flex items-center justify-center">
                <Brain className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold">StudyHub Master</h1>
                <p className="text-sm text-muted-foreground">
                  Painel de Administração
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" onClick={() => navigate("/dashboard")}>
                Ver como Aluno
              </Button>
              <Button variant="outline" onClick={() => navigate("/master/settings")}>
                <Settings className="h-4 w-4 mr-2" />
                Configurações
              </Button>
              <Button variant="ghost" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Sair
              </Button>
            </div>
          </div>
        </div>
      </header>
    );
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simular progresso durante o upload
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Enviar arquivo para o Supabase Storage
      const uploadResult = await uploadFile(file, 'content');

      // Completar progresso
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Atualizar newContent com informações do arquivo (sem URL por segurança)
      setNewContent(prev => ({
        ...prev,
        file_path: uploadResult.path,
        file_size: uploadResult.size,
        file_name: uploadResult.name
      }));

      setSuccessModal({
        isOpen: true,
        title: "Envio Concluído",
        message: `Arquivo "${file.name}" foi enviado com sucesso!`
      });

    } catch (error) {
      console.error('Erro ao enviar arquivo:', error);
      setSuccessModal({
        isOpen: true,
        title: "Erro no Envio",
        message: `Erro ao enviar arquivo: ${error.message || 'Erro desconhecido'}`
      });
    } finally {
      setIsUploading(false);
      // Resetar progresso após um atraso
      setTimeout(() => setUploadProgress(0), 2000);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
      case "ativo":
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
      case "suspended":
      case "suspenso":
        return <Badge variant="destructive">Suspenso</Badge>;
      case "approved":
      case "aprovado":
        return <Badge className="bg-green-100 text-green-800">Aprovado</Badge>;
      case "pending":
      case "pendente":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">Pendente</Badge>
        );
      case "paid":
      case "pago":
        return <Badge className="bg-green-100 text-green-800">Pago</Badge>;
      case "cancelled":
      case "cancelado":
        return <Badge variant="destructive">Cancelado</Badge>;
      case "processing":
      case "processando":
        return <Badge className="bg-blue-100 text-blue-800">Processando</Badge>;
      case "published":
      case "publicado":
        return <Badge className="bg-green-100 text-green-800">Publicado</Badge>;
      case "draft":
      case "rascunho":
        return <Badge className="bg-gray-100 text-gray-800">Rascunho</Badge>;
      case "archived":
      case "arquivado":
        return <Badge className="bg-gray-100 text-gray-600">Arquivado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Estatísticas calculadas a partir de dados reais
  const stats = {
    totalStudents: allStudentsData.length,
    activeStudents: allStudentsData.filter((s) => s.status === "approved").length,
    pendingStudents: pendingStudents.length,
    suspendedStudents: allStudentsData.filter((s) => s.status === "suspended").length,
    totalRevenue: paymentsData
      .filter(p => p.status === "pago")
      .reduce((sum, p) => sum + (p.amount || 0), 0),
    monthlyRevenue: paymentsData
      .filter(p => p.status === "pago" && isCurrentMonth(p.date))
      .reduce((sum, p) => sum + (p.amount || 0), 0),
    totalContent: contentData.length,
    pendingPayments: paymentsData.filter(p => p.status === "pendente").length,
  };

  const filteredContent = contentData.filter(content =>
    content.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    content.type?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredStudents = allStudentsData.filter(student =>
    student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.course?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Função auxiliar para verificar se a data está no mês atual
  function isCurrentMonth(dateString: string) {
    if (!dateString) return false;
    const date = new Date(dateString);
    const now = new Date();
    return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
  }

  const handlePublishContent = async () => {
    if (!newContent.title || !newContent.category) {
      setSuccessModal({
        isOpen: true,
        title: "Campos Obrigatórios",
        message: "Por favor, preencha o título e selecione uma categoria."
      });
      return;
    }

    try {
      // Criar conteúdo no banco de dados (armazenar file_path em vez de file_url por segurança)
      await createContent({
        title: newContent.title,
        description: newContent.description,
        type: newContent.type,
        file_url: newContent.file_path, // Armazenar caminho, não URL
        file_size: newContent.file_size,
      });

      // Resetar formulário
      setNewContent({
        title: "",
        description: "",
        category: "",
        type: "video",
        file_path: "",
        file_size: 0,
        file_name: ""
      });

      // Recarregar dados do conteúdo
      await loadData();

      setSuccessModal({
        isOpen: true,
        title: "Conteúdo Publicado",
        message: "Conteúdo foi publicado com sucesso e está disponível para os alunos!"
      });

    } catch (error) {
      console.error('Erro ao publicar conteúdo:', error);
      setSuccessModal({
        isOpen: true,
        title: "Erro",
        message: "Erro ao publicar conteúdo. Tente novamente."
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header />

      <div className="container mx-auto px-4 py-8">
        {/* Dashboard Overview */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-6">Visão Geral</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-2xl font-bold">{stats.totalStudents}</p>
                <p className="text-xs text-muted-foreground">Total Alunos</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold">{stats.activeStudents}</p>
                <p className="text-xs text-muted-foreground">Aprovados</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
                <p className="text-2xl font-bold">
                  R$ {stats.totalRevenue.toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground">Receita Total</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <p className="text-2xl font-bold">
                  R$ {stats.monthlyRevenue.toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground">Este Mês</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-cyan-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <FileText className="h-6 w-6 text-cyan-600" />
                </div>
                <p className="text-2xl font-bold">{stats.totalContent}</p>
                <p className="text-xs text-muted-foreground">Conteúdos</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
                <p className="text-2xl font-bold">{stats.pendingStudents}</p>
                <p className="text-xs text-muted-foreground">
                  Aguardando Aprovação
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="upload">Envio</TabsTrigger>
            <TabsTrigger value="conteudo">Conteúdo</TabsTrigger>
            <TabsTrigger value="alunos">Alunos</TabsTrigger>
            <TabsTrigger value="financeiro">Financeiro</TabsTrigger>
            <TabsTrigger value="relatorios">Relatórios</TabsTrigger>
          </TabsList>

          {/* Aba de Envio */}
          <TabsContent value="upload">
            <div className="grid lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Upload className="h-5 w-5" />
                    <span>Envio de Conteúdo</span>
                  </CardTitle>
                  <CardDescription>
                    Envie vídeos, apostilas e outros materiais
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Título</Label>
                      <Input
                        id="title"
                        value={newContent.title}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            title: e.target.value,
                          })
                        }
                        placeholder="Ex: Neuro - Atenção"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">Categoria</Label>
                      <select
                        id="category"
                        value={newContent.category}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            category: e.target.value,
                          })
                        }
                        className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="">Selecione...</option>
                        <option value="matematica">
                          TCC - Infancia e adolescencia
                        </option>
                        <option value="contabilidade">TCC - Adultos</option>
                        <option value="gestao">Análise do Comportamento</option>
                        <option value="marketing">Neuropsicologia</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Descrição</Label>
                    <Textarea
                      id="description"
                      value={newContent.description}
                      onChange={(e) =>
                        setNewContent({
                          ...newContent,
                          description: e.target.value,
                        })
                      }
                      placeholder="Descreva o conteúdo..."
                      rows={3}
                    />
                  </div>

                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="type">Tipo</Label>
                      <select
                        id="type"
                        value={newContent.type}
                        onChange={(e) =>
                          setNewContent({ ...newContent, type: e.target.value })
                        }
                        className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="video">Vídeo</option>
                        <option value="document">Documento</option>
                        <option value="presentation">Apresentação</option>
                        <option value="exercise">Exercício</option>
                      </select>
                    </div>
                  </div>

                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                    <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-lg font-medium mb-2">
                      Arraste arquivos aqui ou clique para selecionar
                    </p>
                    <p className="text-sm text-muted-foreground mb-4">
                      Suporte: MP4, PDF, PPTX, DOCX (até 5GB)
                    </p>
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                      accept=".mp4,.pdf,.pptx,.docx"
                    />
                    <Button
                      onClick={() =>
                        document.getElementById("file-upload")?.click()
                      }
                      className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                    >
                      Selecionar Arquivo
                    </Button>
                  </div>

                  {isUploading && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Enviando arquivo...</span>
                        <span>{uploadProgress}%</span>
                      </div>
                      <Progress value={uploadProgress} />
                    </div>
                  )}

                  <Button
                    className="w-full bg-gradient-to-r from-brain-500 to-cognitive-500"
                    disabled={!newContent.title || !newContent.category}
                    onClick={handlePublishContent}
                  >
                    Publicar Conteúdo
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Envios Recentes</CardTitle>
                  <CardDescription>
                    Acompanhe o status dos seus envios
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {contentData.length > 0 ? (
                    contentData.slice(0, 5).map((upload) => (
                      <div
                        key={upload.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-muted rounded">
                            {upload.type === "video" ? (
                              <Video className="h-4 w-4" />
                            ) : (
                              <FileText className="h-4 w-4" />
                            )}
                          </div>
                          <div>
                            <h4 className="font-medium text-sm">
                              {upload.title}
                            </h4>
                            <p className="text-xs text-muted-foreground">
                              {upload.size || "Tamanho não disponível"} • {upload.uploadDate || "Data não disponível"}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getStatusBadge(upload.status)}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleViewContent(upload.id)}
                            title="Visualizar"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDeleteContent(upload.id)}
                            title="Remover"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">
                        Nenhum envio recente
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Envie arquivos para vê-los aqui
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Aba de Gerenciamento de Conteúdo */}
          <TabsContent value="conteudo">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Gerenciar Conteúdo</CardTitle>
                    <CardDescription>
                      Visualize e edite todo o conteúdo da plataforma
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={handleToggleFilters}>
                      <Filter className="h-4 w-4 mr-2" />
                      Filtros
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Buscar por título, categoria ou tipo..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  {filteredContent.length > 0 ? (
                    filteredContent.map((content) => (
                      <div
                        key={content.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="p-3 bg-brain-50 rounded-lg">
                            {content.type === "video" ? (
                              <Video className="h-5 w-5 text-brain-600" />
                            ) : (
                              <FileText className="h-5 w-5 text-brain-600" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-semibold">{content.title}</h3>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                              <span>
                                📊 {content.views || content.downloads || 0}{" "}
                                {content.type === "video"
                                  ? "visualizações"
                                  : "downloads"}
                              </span>
                              <span>📅 {content.uploadDate || "Data não disponível"}</span>
                              <span>💾 {content.size || "Tamanho não disponível"}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getStatusBadge(content.status)}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditContent(content.id)}
                            title="Editar conteúdo"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleViewContent(content.id)}
                            title="Visualizar conteúdo"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDeleteContent(content.id)}
                            title="Deletar conteúdo"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">
                        Nenhum conteúdo encontrado
                      </h3>
                      <p className="text-muted-foreground">
                        {searchTerm
                          ? "Nenhum conteúdo corresponde à sua busca"
                          : "Envie conteúdo na aba 'Envio' para começar"
                        }
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba de Gerenciamento de Alunos */}
          <TabsContent value="alunos">
            <div className="space-y-6">
              {/* Aprovações Pendentes */}
              {pendingStudents.length > 0 && (
                <Card className="border-yellow-200 bg-yellow-50">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-yellow-800">
                      <AlertCircle className="h-5 w-5" />
                      <span>
                        Aprovações Pendentes ({pendingStudents.length})
                      </span>
                    </CardTitle>
                    <CardDescription>
                      Alunos aguardando aprovação para acessar a plataforma
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {pendingStudents.map((student) => (
                        <div
                          key={student.id}
                          className="flex items-center justify-between p-4 bg-white border rounded-lg"
                        >
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-brain-100 to-cognitive-100 rounded-full flex items-center justify-center">
                              <span className="font-semibold text-brain-700">
                                {student.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </span>
                            </div>
                            <div>
                              <h3 className="font-semibold">{student.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {student.email}
                              </p>
                              <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                                <span>{student.course}</span>
                                <span>{student.semester}</span>
                                <span>
                                  Cadastrou-se:{" "}
                                  {new Date(
                                    student.created_at,
                                  ).toLocaleDateString("pt-BR")}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              onClick={() => handleApproveStudent(student.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Aprovar
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleSuspendStudent(student.id)}
                            >
                              <X className="h-4 w-4 mr-1" />
                              Rejeitar
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Todos os Alunos */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Todos os Alunos</CardTitle>
                      <CardDescription>
                        Gerenciar todos os estudantes da plataforma
                      </CardDescription>
                    </div>

                  </div>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Buscar por nome, email ou curso..."
                        className="pl-10"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    {filteredStudents.map((student) => (
                      <div
                        key={student.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-brain-100 to-cognitive-100 rounded-full flex items-center justify-center">
                            <span className="font-semibold text-brain-700">
                              {student.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </span>
                          </div>
                          <div>
                            <h3 className="font-semibold">{student.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {student.email}
                            </p>
                            <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                              <span>{student.course}</span>
                              <span>{student.semester}</span>
                              <span>
                                Membro desde:{" "}
                                {new Date(
                                  student.created_at,
                                ).toLocaleDateString("pt-BR")}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          {getStatusBadge(student.status)}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditStudent(student.id)}
                            title="Editar aluno"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {student.status === "approved" && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleBlockStudent(student.id)}
                              title="Bloquear aluno"
                            >
                              <Shield className="h-4 w-4" />
                            </Button>
                          )}
                          {student.status === "suspended" && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleApproveStudent(student.id)}
                              title="Desbloquear aluno"
                              className="text-green-600 hover:text-green-700"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  {allStudentsData.length === 0 && !isLoading && (
                    <div className="text-center py-8">
                      <Users className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">
                        Nenhum aluno cadastrado
                      </h3>
                      <p className="text-muted-foreground">
                        Os alunos aparecerão aqui após se cadastrarem na
                        plataforma
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Aba Financeira */}
          <TabsContent value="financeiro">
            <FinancialManagement
              students={allStudentsData}
              onUpdatePayment={handleUpdatePaymentStatus}
            />
          </TabsContent>

          {/* Aba de Relatórios */}
          <TabsContent value="relatorios">
            <div className="grid lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5" />
                    <span>Relatórios de Uso</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Aulas ao Vivo Assistidas</span>
                      <span className="font-semibold">2,341</span>
                    </div>
                    <Progress value={85} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Vídeos Reproduzidos</span>
                      <span className="font-semibold">5,678</span>
                    </div>
                    <Progress value={72} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Materiais Baixados</span>
                      <span className="font-semibold">1,234</span>
                    </div>
                    <Progress value={68} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Mensagens no Chat</span>
                      <span className="font-semibold">8,901</span>
                    </div>
                    <Progress value={91} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Exportar Relatórios</CardTitle>
                  <CardDescription>
                    Gere relatórios detalhados em diferentes formatos
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleExportReport("Alunos (CSV)")}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Relatório de Alunos (CSV)
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleExportReport("Financeiro (PDF)")}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Relatório Financeiro (PDF)
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleExportReport("Conteúdo (XLSX)")}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Relatório de Conteúdo (XLSX)
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleExportReport("Engajamento (PDF)")}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Relatório de Engajamento (PDF)
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Modais */}
      <ConfirmModal
        isOpen={confirmModal.isOpen}
        onClose={() => setConfirmModal(prev => ({ ...prev, isOpen: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
        variant={confirmModal.variant}
      />

      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal(prev => ({ ...prev, isOpen: false }))}
        title={successModal.title}
        message={successModal.message}
      />

      <StudentProfileModal
        isOpen={studentModal.isOpen}
        onClose={() => setStudentModal(prev => ({ ...prev, isOpen: false }))}
        student={studentModal.student}
        onUpdate={loadData}
      />

      {/* Visualizador Seguro de Arquivos */}
      <SecureFileViewer
        isOpen={viewerModal.isOpen}
        onClose={() => setViewerModal(prev => ({ ...prev, isOpen: false }))}
        filePath={viewerModal.filePath}
        fileName={viewerModal.fileName}
        fileType={viewerModal.fileType}
        title={viewerModal.title}
      />
    </div>
  );
};

export default MasterPanel;
