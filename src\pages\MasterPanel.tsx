import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  Upload,
  Users,
  CreditCard,
  FileText,
  Video,
  Calendar,
  Settings,
  BarChart3,
  Download,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Shield,
  X,
  LogOut,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  getPendingStudents,
  getAllStudents,
  updateUserStatus,
  getCurrentUser,
  getUserProfile,
  Profile,
  signOut,
} from "@/lib/supabase";

const MasterPanel = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [pendingStudents, setPendingStudents] = useState<Profile[]>([]);
  const [allStudentsData, setAllStudentsData] = useState<Profile[]>([]);
  const [currentUser, setCurrentUser] = useState<Profile | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedContent, setSelectedContent] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Estados para diferentes seções
  const [newContent, setNewContent] = useState({
    title: "",
    description: "",
    category: "",
    type: "video",
    price: "",
    duration: "",
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);

      // Verificar se é um usuário master
      const user = await getCurrentUser();
      if (user) {
        const profile = await getUserProfile(user.id);
        if (!profile || profile.user_type !== "master") {
          // If not a master user, redirect to dashboard
          navigate("/dashboard");
          return;
        }
        setCurrentUser(profile);
      } else {
        // If not logged in, redirect to login
        navigate("/login");
        return;
      }

      // Carregar alunos pendentes e todos os alunos
      const [pending, all] = await Promise.all([
        getPendingStudents(),
        getAllStudents(),
      ]);

      setPendingStudents(pending);
      setAllStudentsData(all);
    } catch (error) {
      console.error("Erro ao carregar dados:", error);
      // On error, redirect to dashboard
      navigate("/dashboard");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproveStudent = async (studentId: string) => {
    try {
      await updateUserStatus(studentId, "approved", currentUser?.id);
      await loadData(); // Recarregar dados
      alert("Aluno aprovado com sucesso!");
    } catch (error) {
      console.error("Erro ao aprovar aluno:", error);
      alert("Erro ao aprovar aluno");
    }
  };

  const handleSuspendStudent = async (studentId: string) => {
    try {
      await updateUserStatus(studentId, "suspended", currentUser?.id);
      await loadData();
      alert("Aluno suspenso com sucesso!");
    } catch (error) {
      console.error("Erro ao suspender aluno:", error);
      alert("Erro ao suspender aluno");
    }
  };

  const handleDeleteContent = (contentId: string) => {
    // In a real app, this would delete from database
    alert(`Conteúdo ${contentId} seria deletado`);
  };

  const handleEditContent = (contentId: string) => {
    // In a real app, this would open edit modal
    alert(`Editando conteúdo ${contentId}`);
  };

  const handleViewContent = (contentId: string) => {
    // In a real app, this would open content viewer
    alert(`Visualizando conteúdo ${contentId}`);
  };

  const handleNewContent = () => {
    // In a real app, this would open content creation modal
    alert("Abrindo formulário de novo conteúdo");
  };

  const handleNewStudent = () => {
    // In a real app, this would open student creation modal
    alert("Abrindo formulário de novo aluno");
  };

  const handleExportReport = (reportType: string) => {
    // In a real app, this would generate and download the report
    alert(`Gerando relatório: ${reportType}`);
  };

  const handleToggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const recentUploads = [
    {
      id: 1,
      title: "Matemática Financeira - Aula 10",
      type: "video",
      size: "2.1 GB",
      uploadDate: "2 horas atrás",
      status: "processando",
      views: 0,
    },
    {
      id: 2,
      title: "Apostila Contabilidade Avançada",
      type: "document",
      size: "15.8 MB",
      uploadDate: "1 dia atrás",
      status: "publicado",
      downloads: 45,
    },
    {
      id: 3,
      title: "Exercícios Gestão de Projetos",
      type: "document",
      size: "3.2 MB",
      uploadDate: "3 dias atrás",
      status: "publicado",
      downloads: 128,
    },
  ];

  const students = [
    {
      id: 1,
      name: "Maria Santos",
      email: "<EMAIL>",
      course: "Administração",
      semester: "5º período",
      status: "ativo",
      lastAccess: "2 horas atrás",
      progress: 78,
    },
    {
      id: 2,
      name: "Pedro Lima",
      email: "<EMAIL>",
      course: "Contabilidade",
      semester: "3º período",
      status: "ativo",
      lastAccess: "1 dia atrás",
      progress: 65,
    },
    {
      id: 3,
      name: "Ana Silva",
      email: "<EMAIL>",
      course: "Gestão",
      semester: "7º período",
      status: "suspenso",
      lastAccess: "1 semana atrás",
      progress: 42,
    },
  ];

  const payments = [
    {
      id: 1,
      student: "Carlos Mendes",
      plan: "Premium Anual",
      amount: 597.0,
      date: "15/01/2024",
      status: "aprovado",
      method: "Cartão de Crédito",
    },
    {
      id: 2,
      student: "Julia Costa",
      plan: "Premium Mensal",
      amount: 59.9,
      date: "14/01/2024",
      status: "pendente",
      method: "PIX",
    },
    {
      id: 3,
      student: "Roberto Oliveira",
      plan: "Premium Semestral",
      amount: 297.0,
      date: "13/01/2024",
      status: "cancelado",
      method: "Boleto",
    },
  ];

  const Header = () => {
    const handleLogout = async () => {
      try {
        await signOut();
        navigate("/login");
      } catch (error) {
        console.error("Error logging out:", error);
      }
    };

    return (
      <header className="border-b bg-card/95 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-brain-500 to-cognitive-500 rounded-lg flex items-center justify-center">
                <Brain className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold">StudyHub Master</h1>
                <p className="text-sm text-muted-foreground">
                  Painel de Administração
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" onClick={() => navigate("/dashboard")}>
                Ver como Aluno
              </Button>
              <Button variant="outline" onClick={() => navigate("/master/settings")}>
                <Settings className="h-4 w-4 mr-2" />
                Configurações
              </Button>
              <Button variant="ghost" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Sair
              </Button>
            </div>
          </div>
        </div>
      </header>
    );
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsUploading(true);
      setUploadProgress(0);

      // Simular upload
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            setIsUploading(false);
            return 100;
          }
          return prev + 10;
        });
      }, 500);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ativo":
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
      case "suspenso":
        return <Badge variant="destructive">Suspenso</Badge>;
      case "aprovado":
        return <Badge className="bg-green-100 text-green-800">Aprovado</Badge>;
      case "pendente":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">Pendente</Badge>
        );
      case "cancelado":
        return <Badge variant="destructive">Cancelado</Badge>;
      case "processando":
        return <Badge className="bg-blue-100 text-blue-800">Processando</Badge>;
      case "publicado":
        return <Badge className="bg-green-100 text-green-800">Publicado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Calculated stats and filtered data
  const stats = {
    totalStudents: allStudentsData.length,
    activeStudents: allStudentsData.filter((s) => s.status === "approved").length,
    pendingStudents: pendingStudents.length,
    suspendedStudents: allStudentsData.filter((s) => s.status === "suspended").length,
    totalRevenue: 84750, // Would be calculated from real payments
    monthlyRevenue: 12350, // Would be calculated from real payments
    totalContent: 156, // Would be calculated from real content
    pendingPayments: payments.filter(p => p.status === "pendente").length,
  };

  const filteredContent = recentUploads.filter(content =>
    content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    content.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredStudents = allStudentsData.filter(student =>
    student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.course?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header />

      <div className="container mx-auto px-4 py-8">
        {/* Dashboard Overview */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-6">Visão Geral</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-2xl font-bold">{stats.totalStudents}</p>
                <p className="text-xs text-muted-foreground">Total Alunos</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold">{stats.activeStudents}</p>
                <p className="text-xs text-muted-foreground">Aprovados</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
                <p className="text-2xl font-bold">
                  R$ {stats.totalRevenue.toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground">Receita Total</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <p className="text-2xl font-bold">
                  R$ {stats.monthlyRevenue.toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground">Este Mês</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-cyan-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <FileText className="h-6 w-6 text-cyan-600" />
                </div>
                <p className="text-2xl font-bold">{stats.totalContent}</p>
                <p className="text-xs text-muted-foreground">Conteúdos</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
                <p className="text-2xl font-bold">{stats.pendingStudents}</p>
                <p className="text-xs text-muted-foreground">
                  Aguardando Aprovação
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        <Tabs defaultValue="upload" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="upload">Upload</TabsTrigger>
            <TabsTrigger value="conteudo">Conteúdo</TabsTrigger>
            <TabsTrigger value="alunos">Alunos</TabsTrigger>
            <TabsTrigger value="financeiro">Financeiro</TabsTrigger>
            <TabsTrigger value="relatorios">Relatórios</TabsTrigger>
          </TabsList>

          {/* Upload Tab */}
          <TabsContent value="upload">
            <div className="grid lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Upload className="h-5 w-5" />
                    <span>Upload de Conteúdo</span>
                  </CardTitle>
                  <CardDescription>
                    Faça upload de vídeos, apostilas e outros materiais
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Título</Label>
                      <Input
                        id="title"
                        value={newContent.title}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            title: e.target.value,
                          })
                        }
                        placeholder="Ex: Matemática Financeira - Aula 1"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">Categoria</Label>
                      <select
                        id="category"
                        value={newContent.category}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            category: e.target.value,
                          })
                        }
                        className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="">Selecione...</option>
                        <option value="matematica">
                          Matemática Financeira
                        </option>
                        <option value="contabilidade">Contabilidade</option>
                        <option value="gestao">Gestão de Projetos</option>
                        <option value="marketing">Marketing Digital</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Descrição</Label>
                    <Textarea
                      id="description"
                      value={newContent.description}
                      onChange={(e) =>
                        setNewContent({
                          ...newContent,
                          description: e.target.value,
                        })
                      }
                      placeholder="Descreva o conteúdo..."
                      rows={3}
                    />
                  </div>

                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="type">Tipo</Label>
                      <select
                        id="type"
                        value={newContent.type}
                        onChange={(e) =>
                          setNewContent({ ...newContent, type: e.target.value })
                        }
                        className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="video">Vídeo</option>
                        <option value="document">Documento</option>
                        <option value="presentation">Apresentação</option>
                        <option value="exercise">Exercício</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="duration">Duração (min)</Label>
                      <Input
                        id="duration"
                        type="number"
                        value={newContent.duration}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            duration: e.target.value,
                          })
                        }
                        placeholder="45"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="price">Preço (R$)</Label>
                      <Input
                        id="price"
                        type="number"
                        value={newContent.price}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            price: e.target.value,
                          })
                        }
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                    <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-lg font-medium mb-2">
                      Arraste arquivos aqui ou clique para selecionar
                    </p>
                    <p className="text-sm text-muted-foreground mb-4">
                      Suporte: MP4, PDF, PPTX, DOCX (até 5GB)
                    </p>
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                      accept=".mp4,.pdf,.pptx,.docx"
                    />
                    <Button
                      onClick={() =>
                        document.getElementById("file-upload")?.click()
                      }
                      className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                    >
                      Selecionar Arquivo
                    </Button>
                  </div>

                  {isUploading && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Fazendo upload...</span>
                        <span>{uploadProgress}%</span>
                      </div>
                      <Progress value={uploadProgress} />
                    </div>
                  )}

                  <Button
                    className="w-full bg-gradient-to-r from-brain-500 to-cognitive-500"
                    disabled={!newContent.title || !newContent.category}
                  >
                    Publicar Conteúdo
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Uploads Recentes</CardTitle>
                  <CardDescription>
                    Acompanhe o status dos seus uploads
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recentUploads.map((upload) => (
                    <div
                      key={upload.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-muted rounded">
                          {upload.type === "video" ? (
                            <Video className="h-4 w-4" />
                          ) : (
                            <FileText className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-sm">
                            {upload.title}
                          </h4>
                          <p className="text-xs text-muted-foreground">
                            {upload.size} • {upload.uploadDate}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(upload.status)}
                        <Button size="sm" variant="ghost">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Content Management Tab */}
          <TabsContent value="conteudo">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Gerenciar Conteúdo</CardTitle>
                    <CardDescription>
                      Visualize e edite todo o conteúdo da plataforma
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={handleToggleFilters}>
                      <Filter className="h-4 w-4 mr-2" />
                      Filtros
                    </Button>
                    <Button
                      className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                      onClick={handleNewContent}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Novo Conteúdo
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Buscar por título, categoria ou tipo..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  {filteredContent.map((content) => (
                    <div
                      key={content.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-brain-50 rounded-lg">
                          {content.type === "video" ? (
                            <Video className="h-5 w-5 text-brain-600" />
                          ) : (
                            <FileText className="h-5 w-5 text-brain-600" />
                          )}
                        </div>
                        <div>
                          <h3 className="font-semibold">{content.title}</h3>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>
                              📊 {content.views || content.downloads}{" "}
                              {content.type === "video"
                                ? "visualizações"
                                : "downloads"}
                            </span>
                            <span>📅 {content.uploadDate}</span>
                            <span>💾 {content.size}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(content.status)}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEditContent(content.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleViewContent(content.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDeleteContent(content.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Students Management Tab */}
          <TabsContent value="alunos">
            <div className="space-y-6">
              {/* Pending Approvals */}
              {pendingStudents.length > 0 && (
                <Card className="border-yellow-200 bg-yellow-50">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-yellow-800">
                      <AlertCircle className="h-5 w-5" />
                      <span>
                        Aprovações Pendentes ({pendingStudents.length})
                      </span>
                    </CardTitle>
                    <CardDescription>
                      Alunos aguardando aprovação para acessar a plataforma
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {pendingStudents.map((student) => (
                        <div
                          key={student.id}
                          className="flex items-center justify-between p-4 bg-white border rounded-lg"
                        >
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-brain-100 to-cognitive-100 rounded-full flex items-center justify-center">
                              <span className="font-semibold text-brain-700">
                                {student.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </span>
                            </div>
                            <div>
                              <h3 className="font-semibold">{student.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {student.email}
                              </p>
                              <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                                <span>{student.course}</span>
                                <span>{student.semester}</span>
                                <span>
                                  Cadastrou-se:{" "}
                                  {new Date(
                                    student.created_at,
                                  ).toLocaleDateString("pt-BR")}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              onClick={() => handleApproveStudent(student.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Aprovar
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleSuspendStudent(student.id)}
                            >
                              <X className="h-4 w-4 mr-1" />
                              Rejeitar
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* All Students */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Todos os Alunos</CardTitle>
                      <CardDescription>
                        Gerenciar todos os estudantes da plataforma
                      </CardDescription>
                    </div>
                    <Button
                      className="bg-gradient-to-r from-brain-500 to-cognitive-500"
                      onClick={handleNewStudent}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Novo Aluno
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Buscar por nome, email ou curso..."
                        className="pl-10"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    {filteredStudents.map((student) => (
                      <div
                        key={student.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-brain-100 to-cognitive-100 rounded-full flex items-center justify-center">
                            <span className="font-semibold text-brain-700">
                              {student.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </span>
                          </div>
                          <div>
                            <h3 className="font-semibold">{student.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {student.email}
                            </p>
                            <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                              <span>{student.course}</span>
                              <span>{student.semester}</span>
                              <span>
                                Membro desde:{" "}
                                {new Date(
                                  student.created_at,
                                ).toLocaleDateString("pt-BR")}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          {getStatusBadge(student.status)}
                          <Button size="sm" variant="ghost">
                            <Edit className="h-4 w-4" />
                          </Button>
                          {student.status === "approved" && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleSuspendStudent(student.id)}
                            >
                              <Shield className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  {allStudentsData.length === 0 && !isLoading && (
                    <div className="text-center py-8">
                      <Users className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">
                        Nenhum aluno cadastrado
                      </h3>
                      <p className="text-muted-foreground">
                        Os alunos aparecerão aqui após se cadastrarem na
                        plataforma
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Financial Tab */}
          <TabsContent value="financeiro">
            <div className="space-y-6">
              <div className="grid md:grid-cols-3 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Receita Mensal
                        </p>
                        <p className="text-2xl font-bold">
                          R$ {stats.monthlyRevenue.toLocaleString()}
                        </p>
                      </div>
                      <div className="p-3 bg-green-100 rounded-full">
                        <TrendingUp className="h-6 w-6 text-green-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Pagamentos Pendentes
                        </p>
                        <p className="text-2xl font-bold">
                          {stats.pendingPayments}
                        </p>
                      </div>
                      <div className="p-3 bg-yellow-100 rounded-full">
                        <Clock className="h-6 w-6 text-yellow-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Taxa de Conversão
                        </p>
                        <p className="text-2xl font-bold">23.4%</p>
                      </div>
                      <div className="p-3 bg-blue-100 rounded-full">
                        <BarChart3 className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Transações Recentes</CardTitle>
                  <CardDescription>
                    Histórico de pagamentos e assinaturas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {payments.map((payment) => (
                      <div
                        key={payment.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div>
                          <h4 className="font-semibold">{payment.student}</h4>
                          <p className="text-sm text-muted-foreground">
                            {payment.plan} • {payment.method}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">
                            R$ {payment.amount.toFixed(2)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {payment.date}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getStatusBadge(payment.status)}
                          <Button size="sm" variant="ghost">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="relatorios">
            <div className="grid lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5" />
                    <span>Relatórios de Uso</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Aulas ao Vivo Assistidas</span>
                      <span className="font-semibold">2,341</span>
                    </div>
                    <Progress value={85} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Vídeos Reproduzidos</span>
                      <span className="font-semibold">5,678</span>
                    </div>
                    <Progress value={72} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Materiais Baixados</span>
                      <span className="font-semibold">1,234</span>
                    </div>
                    <Progress value={68} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Mensagens no Chat</span>
                      <span className="font-semibold">8,901</span>
                    </div>
                    <Progress value={91} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Exportar Relatórios</CardTitle>
                  <CardDescription>
                    Gere relatórios detalhados em diferentes formatos
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleExportReport("Alunos (CSV)")}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Relatório de Alunos (CSV)
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleExportReport("Financeiro (PDF)")}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Relatório Financeiro (PDF)
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleExportReport("Conteúdo (XLSX)")}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Relatório de Conteúdo (XLSX)
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleExportReport("Engajamento (PDF)")}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Relatório de Engajamento (PDF)
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MasterPanel;
