import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Calendar,
  Clock,
  Users,
  Video,
  Mic,
  MicOff,
  VideoOff,
  PhoneCall,
  MessageCircle,
  Hand,
  Settings,
  Maximize,
  Volume2,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";

const LiveClasses = () => {
  const navigate = useNavigate();
  const [isJoined, setIsJoined] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [isVideoOff, setIsVideoOff] = useState(true);

  const upcomingClasses = [
    {
      id: 1,
      title: "Matemática Financeira - <PERSON><PERSON>",
      professor: "<PERSON><PERSON> <PERSON>",
      time: "14:00 - 15:30",
      date: "Hoje",
      participants: 28,
      status: "live",
      description: "Aplicações práticas de juros compostos em investimentos",
    },
    {
      id: 2,
      title: "Contabilidade Geral - Demonstrações",
      professor: "Profa. Ana Costa",
      time: "16:00 - 17:30",
      date: "Hoje",
      participants: 0,
      status: "scheduled",
      description: "Análise das demonstrações contábeis básicas",
    },
    {
      id: 3,
      title: "Gestão de Projetos - Metodologias Ágeis",
      professor: "Prof. Carlos Mendes",
      time: "09:00 - 10:30",
      date: "Amanhã",
      participants: 0,
      status: "scheduled",
      description: "Introdução ao Scrum e Kanban",
    },
  ];

  const liveClass = upcomingClasses[0];

  if (isJoined) {
    return (
      <div className="min-h-screen bg-background">
        <Header title="StudyHub" />

        {/* Live Class Interface */}
        <div className="flex h-[calc(100vh-80px)]">
          {/* Main Video Area */}
          <div className="flex-1 flex flex-col bg-black">
            <div className="flex-1 relative">
              {/* Professor Video */}
              <div className="w-full h-full bg-gray-900 flex items-center justify-center">
                <div className="text-center text-white">
                  <Video className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg">Prof. João Silva</p>
                  <p className="text-sm opacity-75">Matemática Financeira</p>
                </div>
              </div>

              {/* Student Video Grid */}
              <div className="absolute bottom-4 right-4 grid grid-cols-2 gap-2">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div
                    key={i}
                    className="w-24 h-16 bg-gray-800 rounded border-2 border-gray-600 flex items-center justify-center"
                  >
                    <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                      <span className="text-xs text-white">{i + 1}</span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Class Info Overlay */}
              <div className="absolute top-4 left-4 bg-black/80 rounded-lg p-3 text-white">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-sm">AO VIVO</span>
                </div>
                <p className="text-xs mt-1">28 participantes</p>
              </div>

              {/* Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-black/80 rounded-lg p-3 flex items-center space-x-4">
                  <Button
                    size="sm"
                    variant={isMuted ? "destructive" : "secondary"}
                    onClick={() => setIsMuted(!isMuted)}
                    className="h-10 w-10 p-0"
                  >
                    {isMuted ? (
                      <MicOff className="h-4 w-4" />
                    ) : (
                      <Mic className="h-4 w-4" />
                    )}
                  </Button>

                  <Button
                    size="sm"
                    variant={isVideoOff ? "destructive" : "secondary"}
                    onClick={() => setIsVideoOff(!isVideoOff)}
                    className="h-10 w-10 p-0"
                  >
                    {isVideoOff ? (
                      <VideoOff className="h-4 w-4" />
                    ) : (
                      <Video className="h-4 w-4" />
                    )}
                  </Button>

                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-10 w-10 p-0 text-white hover:bg-white/20"
                  >
                    <Hand className="h-4 w-4" />
                  </Button>

                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-10 w-10 p-0 text-white hover:bg-white/20"
                  >
                    <Volume2 className="h-4 w-4" />
                  </Button>

                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-10 w-10 p-0 text-white hover:bg-white/20"
                  >
                    <Maximize className="h-4 w-4" />
                  </Button>

                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => setIsJoined(false)}
                  >
                    <PhoneCall className="h-4 w-4 mr-2" />
                    Sair
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Sidebar */}
          <div className="w-80 border-l bg-card flex flex-col">
            <div className="p-4 border-b">
              <h3 className="font-semibold">Chat da Aula</h3>
            </div>

            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {[
                {
                  name: "Maria Santos",
                  message: "Ótima explicação, professor!",
                  time: "14:05",
                },
                {
                  name: "Pedro Lima",
                  message: "Poderia repetir a fórmula?",
                  time: "14:07",
                },
                {
                  name: "Prof. João",
                  message: "Claro! FV = PV × (1 + i)^n",
                  time: "14:08",
                },
                {
                  name: "Ana Silva",
                  message: "Obrigada! Agora entendi",
                  time: "14:09",
                },
              ].map((msg, i) => (
                <div key={i} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{msg.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {msg.time}
                    </span>
                  </div>
                  <p className="text-sm bg-muted p-2 rounded">{msg.message}</p>
                </div>
              ))}
            </div>

            <div className="p-4 border-t">
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Digite sua mensagem..."
                  className="flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <Button size="sm">
                  <MessageCircle className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brain-50/20 to-cognitive-50/20">
      <Header title="StudyHub" />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Aulas ao Vivo</h2>
          <p className="text-muted-foreground">
            Participe das aulas em tempo real e interaja com professores e
            colegas
          </p>
        </div>

        <Tabs defaultValue="hoje" className="space-y-6">
          <TabsList>
            <TabsTrigger value="hoje">Hoje</TabsTrigger>
            <TabsTrigger value="semana">Esta Semana</TabsTrigger>
            <TabsTrigger value="historico">Histórico</TabsTrigger>
          </TabsList>

          <TabsContent value="hoje" className="space-y-6">
            <div className="grid gap-6">
              {upcomingClasses.map((classItem) => (
                <Card key={classItem.id} className="relative">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-xl">
                            {classItem.title}
                          </CardTitle>
                          {classItem.status === "live" && (
                            <Badge variant="destructive" className="bg-red-500">
                              <div className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></div>
                              AO VIVO
                            </Badge>
                          )}
                          {classItem.status === "scheduled" && (
                            <Badge variant="secondary">Agendada</Badge>
                          )}
                        </div>
                        <CardDescription>
                          {classItem.description}
                        </CardDescription>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{classItem.date}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{classItem.time}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Users className="h-4 w-4" />
                            <span>{classItem.participants} participantes</span>
                          </div>
                        </div>
                      </div>

                      <div className="text-right space-y-2">
                        <p className="text-sm font-medium">
                          {classItem.professor}
                        </p>
                        {classItem.status === "live" ? (
                          <Button
                            onClick={() => setIsJoined(true)}
                            className="bg-gradient-to-r from-brain-500 to-cognitive-500 hover:from-brain-600 hover:to-cognitive-600"
                          >
                            <Video className="h-4 w-4 mr-2" />
                            Entrar na Aula
                          </Button>
                        ) : (
                          <Button variant="outline" disabled>
                            <Calendar className="h-4 w-4 mr-2" />
                            Lembrar-me
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="semana">
            <Card>
              <CardContent className="p-8 text-center">
                <Calendar className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">
                  Cronograma da Semana
                </h3>
                <p className="text-muted-foreground">
                  Visualize todas as aulas programadas para esta semana
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="historico">
            <Card>
              <CardContent className="p-8 text-center">
                <Video className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Aulas Anteriores</h3>
                <p className="text-muted-foreground">
                  Acesse gravações de aulas passadas
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default LiveClasses;
