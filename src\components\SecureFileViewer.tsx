import React, { useState, useEffect } from "react";
import { Modal } from "./ui/modal";
import { Button } from "./ui/button";
import { 
  FileText, 
  Video, 
  Image, 
  Volume2, 
  Loader2, 
  AlertCircle,
  Eye,
  X
} from "lucide-react";
import { getSecureFileUrl, getFileStream } from "@/lib/supabase";

interface SecureFileViewerProps {
  isOpen: boolean;
  onClose: () => void;
  filePath: string;
  fileName: string;
  fileType: string;
  title: string;
}

export const SecureFileViewer: React.FC<SecureFileViewerProps> = ({
  isOpen,
  onClose,
  filePath,
  fileName,
  fileType,
  title,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [fileBlob, setFileBlob] = useState<Blob | null>(null);

  useEffect(() => {
    if (isOpen && filePath) {
      loadSecureFile();
    }
    
    // Limpeza quando o modal fecha
    return () => {
      if (fileUrl) {
        URL.revokeObjectURL(fileUrl);
      }
      setFileUrl(null);
      setFileBlob(null);
      setError(null);
    };
  }, [isOpen, filePath]);

  const loadSecureFile = async () => {
    setLoading(true);
    setError(null);

    try {
      // Para vídeos e imagens, usaremos URLs assinadas com expiração curta
      if (fileType === 'video' || fileType === 'image') {
        const signedUrl = await getSecureFileUrl(filePath, 1800); // 30 minutos
        setFileUrl(signedUrl);
      } else {
        // Para documentos, baixaremos o blob para prevenir acesso direto
        const blob = await getFileStream(filePath);
        const blobUrl = URL.createObjectURL(blob);
        setFileUrl(blobUrl);
        setFileBlob(blob);
      }
    } catch (err) {
      console.error('Erro ao carregar arquivo seguro:', err);
      setError('Erro ao carregar arquivo. Verifique suas permissões.');
    } finally {
      setLoading(false);
    }
  };

  const getFileIcon = () => {
    switch (fileType) {
      case 'video':
        return <Video className="h-8 w-8 text-blue-600" />;
      case 'image':
        return <Image className="h-8 w-8 text-green-600" />;
      case 'audio':
        return <Volume2 className="h-8 w-8 text-purple-600" />;
      default:
        return <FileText className="h-8 w-8 text-gray-600" />;
    }
  };

  const renderFileContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">Carregando arquivo seguro...</p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <p className="text-red-600 mb-2">Erro ao carregar arquivo</p>
            <p className="text-sm text-muted-foreground">{error}</p>
            <Button onClick={loadSecureFile} className="mt-4">
              Tentar Novamente
            </Button>
          </div>
        </div>
      );
    }

    if (!fileUrl) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            {getFileIcon()}
            <p className="mt-4 text-muted-foreground">Arquivo não disponível</p>
          </div>
        </div>
      );
    }

    // Renderizar baseado no tipo de arquivo
    switch (fileType) {
      case 'video':
        return (
          <div className="w-full">
            <video
              src={fileUrl}
              controls
              controlsList="nodownload" // Desabilitar botão de download
              onContextMenu={(e) => e.preventDefault()} // Desabilitar clique direito
              className="w-full max-h-[70vh] bg-black"
              style={{ 
                pointerEvents: 'auto',
                userSelect: 'none'
              }}
            >
              Seu navegador não suporta reprodução de vídeo.
            </video>
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                🔒 Este vídeo é protegido e só pode ser assistido dentro da plataforma.
              </p>
            </div>
          </div>
        );

      case 'image':
        return (
          <div className="w-full text-center">
            <img
              src={fileUrl}
              alt={fileName}
              onContextMenu={(e) => e.preventDefault()} // Desabilitar clique direito
              className="max-w-full max-h-[70vh] mx-auto"
              style={{ userSelect: 'none' }}
            />
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                🔒 Esta imagem é protegida e não pode ser baixada.
              </p>
            </div>
          </div>
        );

      case 'audio':
        return (
          <div className="w-full">
            <div className="flex items-center justify-center h-32 bg-gray-100 rounded-lg mb-4">
              <Volume2 className="h-16 w-16 text-gray-400" />
            </div>
            <audio
              src={fileUrl}
              controls
              controlsList="nodownload"
              className="w-full"
            >
              Seu navegador não suporta reprodução de áudio.
            </audio>
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                🔒 Este áudio é protegido e só pode ser ouvido dentro da plataforma.
              </p>
            </div>
          </div>
        );

      default:
        // Para documentos (PDF, DOCX, etc.)
        return (
          <div className="w-full">
            <div className="h-[70vh] border rounded-lg overflow-hidden">
              <iframe
                src={fileUrl}
                className="w-full h-full"
                title={fileName}
                sandbox="allow-same-origin" // Restringir capacidades do iframe
                style={{ 
                  border: 'none',
                  pointerEvents: 'auto'
                }}
              />
            </div>
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                🔒 Este documento é protegido e só pode ser visualizado dentro da plataforma.
              </p>
            </div>
          </div>
        );
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title} size="xl">
      <div className="p-6">
        {/* Cabeçalho de Informações do Arquivo */}
        <div className="flex items-center justify-between mb-6 pb-4 border-b">
          <div className="flex items-center space-x-3">
            {getFileIcon()}
            <div>
              <h3 className="font-semibold">{fileName}</h3>
              <p className="text-sm text-muted-foreground">
                Arquivo protegido • Apenas visualização
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" disabled>
              <Eye className="h-4 w-4 mr-2" />
              Somente Visualização
            </Button>
          </div>
        </div>

        {/* Conteúdo do Arquivo */}
        <div className="min-h-[400px]">
          {renderFileContent()}
        </div>

        {/* Rodapé */}
        <div className="flex justify-end mt-6 pt-4 border-t">
          <Button onClick={onClose}>
            Fechar
          </Button>
        </div>
      </div>

      {/* Prevenir clique direito em todo o modal */}
      <style jsx>{`
        .modal-content {
          user-select: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
        }
      `}</style>
    </Modal>
  );
};
