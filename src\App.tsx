import { useEffect, useState } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Onboarding from "./pages/Onboarding";
import Dashboard from "./pages/Dashboard";
import LiveClasses from "./pages/LiveClasses";
import VideoLibrary from "./pages/VideoLibrary";
import Materials from "./pages/Materials";
import ClassChat from "./pages/ClassChat";
import Settings from "./pages/Settings";
import Notifications from "./pages/Notifications";
import MasterPanel from "./pages/MasterPanel";
import { getCurrentUser, getUserProfile, setupApp } from "./lib/supabase";

const queryClient = new QueryClient();

// Initialize the app
setupApp().catch(console.error);

// Protected route component
const ProtectedRoute = ({ children, requiredRole = null }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState(null);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const user = await getCurrentUser();
        
        if (user) {
          setIsAuthenticated(true);
          
          if (requiredRole) {
            const profile = await getUserProfile(user.id);
            
            // Check if profile exists and has user_type
            if (profile && profile.user_type) {
              setUserRole(profile.user_type);
            } else {
              // Default to student if no user_type
              setUserRole('student');
            }
          }
        }
      } catch (error) {
        console.error("Auth check error:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAuth();
  }, [requiredRole]);

  if (isLoading) {
    return <div className="min-h-screen flex items-center justify-center">Carregando...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && userRole !== requiredRole) {
    return <Navigate to={userRole === "master" ? "/master" : "/dashboard"} replace />;
  }

  return children;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/login" element={<Login />} />
          <Route path="/onboarding" element={<Onboarding />} />
          
          {/* Protected student routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute requiredRole="student">
              <Dashboard />
            </ProtectedRoute>
          } />
          <Route path="/live-classes" element={
            <ProtectedRoute requiredRole="student">
              <LiveClasses />
            </ProtectedRoute>
          } />
          <Route path="/videos" element={
            <ProtectedRoute requiredRole="student">
              <VideoLibrary />
            </ProtectedRoute>
          } />
          <Route path="/materials" element={
            <ProtectedRoute requiredRole="student">
              <Materials />
            </ProtectedRoute>
          } />
          <Route path="/chat" element={
            <ProtectedRoute requiredRole="student">
              <ClassChat />
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <Settings />
            </ProtectedRoute>
          } />
          <Route path="/notifications" element={
            <ProtectedRoute>
              <Notifications />
            </ProtectedRoute>
          } />
          
          {/* Protected master route */}
          <Route path="/master" element={
            <ProtectedRoute requiredRole="master">
              <MasterPanel />
            </ProtectedRoute>
          } />
          
          {/* Catch-all route */}
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
