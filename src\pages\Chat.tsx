import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import { CourseChat } from "@/components/CourseChat";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageCircle, Users, Shield, Info } from "lucide-react";
import { getCurrentUser, getUserProfile } from "@/lib/supabase";

const Chat = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [userCourse, setUserCourse] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState(null);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);
      const user = await getCurrentUser();
      
      if (!user) {
        navigate("/login");
        return;
      }

      const profile = await getUserProfile(user.id);
      if (!profile) {
        navigate("/onboarding");
        return;
      }

      setUserProfile(profile);
      setUserCourse(profile.course);
    } catch (error) {
      console.error("Erro ao carregar dados do usuário:", error);
      navigate("/login");
    } finally {
      setLoading(false);
    }
  };

  const getCourseDisplayName = (course: string) => {
    switch (course) {
      case 'TCC - Infancia e adolescencia':
        return 'TCC - Criança e Adolescente';
      case 'TCC - Adultos':
        return 'TCC - Adultos';
      case 'Análise do Comportamento':
        return 'Análise do Comportamento';
      case 'Neuropsicologia':
        return 'Neuropsicologia';
      default:
        return course;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-brain-50 to-cognitive-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <MessageCircle className="h-12 w-12 animate-pulse mx-auto mb-4 text-brain-500" />
              <p className="text-muted-foreground">Carregando chat...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-brain-50 to-cognitive-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold text-gray-900">
              Chat da Turma
            </h1>
            <p className="text-muted-foreground">
              Converse com outros alunos e professores do seu curso
            </p>
          </div>

          {/* Course Info */}
          {userCourse && (
            <Card className="border-brain-200 bg-brain-50">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-brain-100 rounded-lg">
                    <Users className="h-5 w-5 text-brain-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-brain-900">
                      {getCourseDisplayName(userCourse)}
                    </h3>
                    <p className="text-sm text-brain-600">
                      Você está conversando com alunos e professores deste curso
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Privacy Notice */}
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div className="space-y-1">
                  <h4 className="font-medium text-yellow-900">
                    Privacidade e Segurança
                  </h4>
                  <p className="text-sm text-yellow-700">
                    • Apenas alunos do seu curso podem ver e participar desta conversa<br/>
                    • Professores e administradores podem moderar as mensagens<br/>
                    • Mantenha um ambiente respeitoso e acadêmico
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Chat Component */}
          <CourseChat userCourse={userCourse} />

          {/* Help Info */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="space-y-1">
                  <h4 className="font-medium text-blue-900">
                    Dicas para usar o chat
                  </h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Use o chat para tirar dúvidas sobre o conteúdo do curso</li>
                    <li>• Compartilhe experiências e insights com outros alunos</li>
                    <li>• Seja respeitoso e construtivo nas conversas</li>
                    <li>• Para questões privadas, entre em contato diretamente com o professor</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Chat;
