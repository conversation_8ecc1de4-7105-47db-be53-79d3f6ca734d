-- SQL para corrigir o problema de foreign key constraint
-- Execute este script no Supabase SQL Editor

-- 1. <PERSON><PERSON>, vamos verificar se a constraint existe e removê-la temporariamente
DO $$ 
BEGIN
    -- Remove a foreign key constraint se existir
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'profiles_id_fkey' 
        AND table_name = 'profiles'
    ) THEN
        ALTER TABLE profiles DROP CONSTRAINT profiles_id_fkey;
        RAISE NOTICE 'Foreign key constraint profiles_id_fkey removed';
    END IF;
END $$;

-- 2. Re<PERSON>riar a constraint corretamente referenciando auth.users
ALTER TABLE profiles 
ADD CONSTRAINT profiles_id_fkey 
FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- 3. <PERSON>riar ou atualizar a função trigger para criar perfis automaticamente
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, user_type, status, created_at, name)
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'user_type', 'student'),
    CASE 
      WHEN COALESCE(new.raw_user_meta_data->>'user_type', 'student') = 'master' THEN 'approved'
      ELSE 'pending'
    END,
    now(),
    COALESCE(new.raw_user_meta_data->>'name', '')
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Recriar o trigger se não existir
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 5. Verificar se a tabela profiles tem as colunas necessárias
DO $$
BEGIN
    -- Adicionar coluna user_type se não existir
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'user_type') THEN
        ALTER TABLE profiles ADD COLUMN user_type TEXT DEFAULT 'student';
        RAISE NOTICE 'Added user_type column';
    END IF;
    
    -- Adicionar coluna status se não existir
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'status') THEN
        ALTER TABLE profiles ADD COLUMN status TEXT DEFAULT 'pending';
        RAISE NOTICE 'Added status column';
    END IF;
    
    -- Adicionar coluna course se não existir
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'course') THEN
        ALTER TABLE profiles ADD COLUMN course TEXT;
        RAISE NOTICE 'Added course column';
    END IF;
    
    -- Adicionar coluna name se não existir
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'name') THEN
        ALTER TABLE profiles ADD COLUMN name TEXT;
        RAISE NOTICE 'Added name column';
    END IF;
    
    -- Adicionar coluna phone se não existir
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'phone') THEN
        ALTER TABLE profiles ADD COLUMN phone TEXT;
        RAISE NOTICE 'Added phone column';
    END IF;
    
    -- Adicionar coluna semester se não existir
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'semester') THEN
        ALTER TABLE profiles ADD COLUMN semester TEXT;
        RAISE NOTICE 'Added semester column';
    END IF;
END $$;

-- 6. Habilitar RLS na tabela profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 7. Criar políticas RLS para profiles
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" ON profiles
FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles
FOR UPDATE USING (auth.uid() = id);

DROP POLICY IF EXISTS "Masters can view all profiles" ON profiles;
CREATE POLICY "Masters can view all profiles" ON profiles
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.user_type = 'master'
  )
);

DROP POLICY IF EXISTS "Masters can update all profiles" ON profiles;
CREATE POLICY "Masters can update all profiles" ON profiles
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.user_type = 'master'
  )
);

-- 8. Permitir inserção de perfis pelo trigger
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
CREATE POLICY "Enable insert for authenticated users only" ON profiles
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

RAISE NOTICE 'Database structure fixed successfully!';
