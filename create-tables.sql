-- Run this in Supabase SQL Editor to create the required tables

-- 1. Create courses table
CREATE TABLE IF NOT EXISTS courses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  duration TEXT,
  installment_options INTEGER[] DEFAULT '{1,3,6,12}',
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 2. Create content table
CREATE TABLE IF NOT EXISTS content (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('video', 'document', 'audio', 'image')),
  file_url TEXT,
  file_size BIGINT,
  duration INTEGER, -- in seconds for videos/audio
  status TEXT DEFAULT 'published' CHECK (status IN ('processing', 'published', 'draft', 'archived')),
  views INTEGER DEFAULT 0,
  downloads INTEGER DEFAULT 0,
  course_id UUID REFERENCES courses(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 3. Create payment_plans table
CREATE TABLE IF NOT EXISTS payment_plans (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  installments INTEGER NOT NULL DEFAULT 1,
  installment_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  paid_installments INTEGER DEFAULT 0,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'overdue', 'cancelled')),
  payment_method TEXT DEFAULT 'credit_card' CHECK (payment_method IN ('credit_card', 'debit_card', 'pix', 'bank_slip')),
  next_due_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 4. Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  read BOOLEAN DEFAULT false,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create student_progress table
CREATE TABLE IF NOT EXISTS student_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  content_id UUID REFERENCES content(id) ON DELETE CASCADE,
  progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  completed BOOLEAN DEFAULT false,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  time_spent INTEGER DEFAULT 0, -- in seconds
  UNIQUE(student_id, content_id)
);

-- 6. Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  sender_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  type TEXT DEFAULT 'text' CHECK (type IN ('text', 'file', 'image')),
  file_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Enable RLS on all tables
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE content ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies

-- Courses policies
CREATE POLICY "Masters can manage courses" ON courses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

CREATE POLICY "Students can view active courses" ON courses
  FOR SELECT USING (active = true);

-- Content policies
CREATE POLICY "Masters can manage content" ON content
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

CREATE POLICY "Students can view published content" ON content
  FOR SELECT USING (status = 'published');

-- Payment plans policies
CREATE POLICY "Masters can manage all payment plans" ON payment_plans
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

CREATE POLICY "Students can view their own payment plans" ON payment_plans
  FOR SELECT USING (student_id = auth.uid());

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Masters can create notifications for any user" ON notifications
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

-- Student progress policies
CREATE POLICY "Students can manage their own progress" ON student_progress
  FOR ALL USING (student_id = auth.uid());

CREATE POLICY "Masters can view all student progress" ON student_progress
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'master'
    )
  );

-- Chat messages policies
CREATE POLICY "Users can view all chat messages" ON chat_messages
  FOR SELECT USING (true);

CREATE POLICY "Users can create chat messages" ON chat_messages
  FOR INSERT WITH CHECK (sender_id = auth.uid());

-- 9. Insert some sample data
INSERT INTO courses (name, description, price, duration) VALUES
('Matemática Financeira', 'Curso completo de matemática financeira', 497.00, '40 horas'),
('Contabilidade Geral', 'Fundamentos de contabilidade', 397.00, '30 horas')
ON CONFLICT DO NOTHING;

INSERT INTO content (title, description, type, status) VALUES
('Introdução à Matemática Financeira', 'Vídeo introdutório sobre conceitos básicos', 'video', 'published'),
('Apostila de Contabilidade', 'Material de apoio em PDF', 'document', 'published'),
('Exercícios Práticos', 'Lista de exercícios para prática', 'document', 'published')
ON CONFLICT DO NOTHING;
