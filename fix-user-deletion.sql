-- Fix user deletion issues by updating foreign key constraints
-- Run this in your Supabase SQL Editor

-- Step 1: Drop the existing foreign key constraint on approved_by
ALTER TABLE profiles 
DROP CONSTRAINT IF EXISTS profiles_approved_by_fkey;

-- Step 2: Add the constraint back with ON DELETE SET NULL
-- This means when a user is deleted, any references to them in approved_by will be set to NULL
ALTER TABLE profiles 
ADD CONSTRAINT profiles_approved_by_fkey 
FOREIGN KEY (approved_by) 
REFERENCES auth.users(id) 
ON DELETE SET NULL;

-- Step 3: Also ensure the main id constraint has CASCADE (it should already have this)
-- This ensures when a user is deleted, their profile is also deleted
ALTER TABLE profiles 
DROP CONSTRAINT IF EXISTS profiles_id_fkey;

ALTER TABLE profiles 
ADD CONSTRAINT profiles_id_fkey 
FOREIGN KEY (id) 
REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- Step 4: Check for any other tables that might reference auth.users
-- (Add similar fixes for any other tables you might have)

-- Step 5: Clean up any orphaned data
-- Remove any profiles where the user no longer exists in auth.users
DELETE FROM profiles 
WHERE id NOT IN (
  SELECT id FROM auth.users
);

-- Step 6: Set approved_by to NULL for any users that no longer exist
UPDATE profiles 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL 
AND approved_by NOT IN (
  SELECT id FROM auth.users
);

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';

-- Display current constraints for verification
SELECT 
  tc.constraint_name, 
  tc.table_name, 
  kcu.column_name, 
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name,
  rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
  ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name = 'profiles';
